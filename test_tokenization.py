#!/usr/bin/env python3
"""
Test tokenization to debug issues
"""

from transformers import AutoTokenizer
from finetune_with_three_datasets import FinancialDatasetProcessor

def test_tokenization():
    """Test the tokenization process"""
    
    # Initialize processor
    processor = FinancialDatasetProcessor()
    
    # Load tokenizer
    processor.load_tokenizer("microsoft/DialoGPT-medium")
    
    # Create a small test dataset
    test_data = [
        {"input": "What is a stock?", "output": "A stock represents ownership in a company.", "source": "test"},
        {"input": "Explain bonds", "output": "Bonds are debt securities issued by corporations or governments.", "source": "test"}
    ]
    
    # Convert to HF dataset format
    from datasets import Dataset
    test_dataset = Dataset.from_list(test_data)
    
    print("Original dataset:")
    for i, example in enumerate(test_dataset):
        print(f"Example {i}: {example}")
    
    # Tokenize
    print("\nTokenizing...")
    tokenized_dataset = processor.tokenize_dataset(test_dataset)
    
    print(f"\nTokenized dataset has {len(tokenized_dataset)} examples")
    
    # Check first few examples
    for i in range(min(2, len(tokenized_dataset))):
        example = tokenized_dataset[i]
        print(f"\nTokenized example {i}:")
        print(f"  input_ids length: {len(example['input_ids'])}")
        print(f"  labels length: {len(example['labels'])}")
        print(f"  input_ids: {example['input_ids'][:20]}...")  # First 20 tokens
        print(f"  labels: {example['labels'][:20]}...")  # First 20 tokens
        
        # Decode to check
        decoded = processor.tokenizer.decode(example['input_ids'])
        print(f"  decoded: {decoded[:100]}...")

if __name__ == "__main__":
    test_tokenization()
