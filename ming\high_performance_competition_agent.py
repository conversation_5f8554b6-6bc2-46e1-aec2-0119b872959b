"""
High-Performance Competition Agent
Optimized for full GPU/CPU utilization on the hackathon dataset
"""

import pandas as pd
import numpy as np
import json
import re
import logging
import asyncio
import concurrent.futures
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import multiprocessing as mp
from functools import partial
import time

from typhoon_client import TyphoonAPIClient
from advanced_prompting_engine import AdvancedPromptingEngine, PromptingTechnique

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class QuickAnswer:
    """Lightweight answer structure for high-performance processing"""
    question_id: str
    answer: str
    confidence: float
    question_type: str

class HighPerformanceAgent:
    """High-performance agent optimized for competition dataset"""
    
    def __init__(self, api_key: str, max_workers: int = None):
        self.api_key = api_key
        self.max_workers = max_workers or min(32, (mp.cpu_count() or 1) + 4)
        self.typhoon_client = TyphoonAPIClient(api_key)
        
        # Pre-compiled regex patterns for speed
        self.stock_symbol_pattern = re.compile(r'\$([a-zA-Z]+)')
        self.date_pattern = re.compile(r'(\d{4}-\d{2}-\d{2})')
        self.choice_patterns = [
            re.compile(r'\b([ABCD])\b'),
            re.compile(r'answer[:\s]*([ABCD])', re.IGNORECASE),
            re.compile(r'choice[:\s]*([ABCD])', re.IGNORECASE),
            re.compile(r'คำตอบ[:\s]*([ABCD])', re.IGNORECASE)
        ]
        
        print(f"🚀 High-Performance Agent initialized with {self.max_workers} workers")
    
    def quick_classify_question(self, query: str) -> str:
        """Fast question classification"""
        query_lower = query.lower()
        
        # Quick keyword matching
        if any(kw in query_lower for kw in ['rise', 'fall', 'ขึ้น', 'ลง', 'closing price', 'ราคาปิด']):
            return "stock_prediction"
        elif 'abc asset management' in query_lower or 'sec thailand' in query_lower:
            return "compliance"
        elif 'answer choices:' in query_lower or 'ตัวเลือก:' in query_lower:
            return "multiple_choice"
        else:
            return "multiple_choice"  # Default
    
    def extract_stock_info_fast(self, query: str) -> Dict[str, Any]:
        """Fast stock data extraction"""
        symbol_match = self.stock_symbol_pattern.search(query)
        symbol = symbol_match.group(1) if symbol_match else "UNKNOWN"
        
        date_match = self.date_pattern.search(query)
        target_date = date_match.group(1) if date_match else "UNKNOWN"
        
        # Count positive/negative sentiment words quickly
        positive_words = ['bullish', 'up', 'gain', 'growth', 'positive', 'buy', 'strong']
        negative_words = ['bearish', 'down', 'loss', 'decline', 'negative', 'sell', 'weak']
        
        query_lower = query.lower()
        pos_count = sum(1 for word in positive_words if word in query_lower)
        neg_count = sum(1 for word in negative_words if word in query_lower)
        
        return {
            'symbol': symbol,
            'target_date': target_date,
            'sentiment_score': pos_count - neg_count,
            'has_data': '$' in query and any(char.isdigit() for char in query)
        }
    
    def fast_extract_answer(self, response: str, question_type: str) -> str:
        """Fast answer extraction"""
        if question_type == "stock_prediction":
            response_lower = response.lower()
            if 'rise' in response_lower or 'ขึ้น' in response or 'up' in response_lower:
                return "Rise"
            elif 'fall' in response_lower or 'ลง' in response or 'down' in response_lower:
                return "Fall"
            else:
                return "Rise"  # Default optimistic
        
        else:  # Multiple choice
            for pattern in self.choice_patterns:
                matches = pattern.findall(response)
                if matches:
                    return matches[-1].upper()
            return "A"  # Default
    
    def create_optimized_prompt(self, query: str, question_type: str) -> str:
        """Create optimized prompts for different question types"""
        
        if question_type == "stock_prediction":
            stock_info = self.extract_stock_info_fast(query)
            
            return f"""Stock Price Prediction for {stock_info['symbol']} on {stock_info['target_date']}:

{query[:800]}...

Analysis: Based on the price data and sentiment, predict Rise or Fall.
Sentiment Score: {stock_info['sentiment_score']}

Answer with only: Rise or Fall"""
        
        elif question_type == "compliance":
            return f"""Thai Financial Compliance Question:

{query[:1000]}...

Apply BOT/SEC Thailand regulations and best practices.
Answer with only the letter: A, B, C, or D"""
        
        else:  # multiple_choice
            return f"""Financial Knowledge Question:

{query[:800]}...

Apply financial theory and principles.
Answer with only the letter: A, B, C, or D"""
    
    def process_single_question(self, row_data: Tuple[str, str]) -> QuickAnswer:
        """Process a single question efficiently"""
        question_id, query = row_data
        
        try:
            # Quick classification
            question_type = self.quick_classify_question(query)
            
            # Create optimized prompt
            prompt = self.create_optimized_prompt(query, question_type)
            
            # Get response from Typhoon
            response = self.typhoon_client.query(prompt, temperature=0.3)  # Lower temp for consistency
            
            # Fast answer extraction
            answer = self.fast_extract_answer(response, question_type)
            
            # Quick confidence estimation
            confidence = 0.85 if len(response) > 100 else 0.75
            
            return QuickAnswer(
                question_id=question_id,
                answer=answer,
                confidence=confidence,
                question_type=question_type
            )
            
        except Exception as e:
            logger.warning(f"Error processing {question_id}: {str(e)}")
            return QuickAnswer(
                question_id=question_id,
                answer="A",
                confidence=0.5,
                question_type="error"
            )
    
    def process_batch_parallel(self, questions_batch: List[Tuple[str, str]]) -> List[QuickAnswer]:
        """Process a batch of questions in parallel"""
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all questions in the batch
            future_to_question = {
                executor.submit(self.process_single_question, question_data): question_data[0] 
                for question_data in questions_batch
            }
            
            results = []
            for future in concurrent.futures.as_completed(future_to_question):
                try:
                    result = future.result(timeout=30)  # 30 second timeout per question
                    results.append(result)
                except Exception as e:
                    question_id = future_to_question[future]
                    logger.error(f"Failed to process {question_id}: {str(e)}")
                    results.append(QuickAnswer(question_id, "A", 0.5, "error"))
            
            return results
    
    def generate_submission_fast(self, test_file: str = "test.csv", 
                                output_file: str = "high_performance_submission.csv",
                                batch_size: int = 50) -> pd.DataFrame:
        """Generate submission using full CPU/GPU power"""
        
        print("🚀 HIGH-PERFORMANCE COMPETITION SUBMISSION")
        print("=" * 60)
        print(f"💻 Using {self.max_workers} workers")
        print(f"📦 Batch size: {batch_size}")
        
        start_time = time.time()
        
        # Load dataset
        df = pd.read_csv(test_file)
        total_questions = len(df)
        print(f"📊 Processing {total_questions} questions...")
        
        # Prepare question data
        question_data = [(row['id'], row['query']) for _, row in df.iterrows()]
        
        # Process in batches for memory efficiency
        all_results = []
        
        for i in range(0, len(question_data), batch_size):
            batch = question_data[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(question_data) + batch_size - 1) // batch_size
            
            print(f"🔄 Processing batch {batch_num}/{total_batches} ({len(batch)} questions)...")
            
            batch_start = time.time()
            batch_results = self.process_batch_parallel(batch)
            batch_time = time.time() - batch_start
            
            all_results.extend(batch_results)
            
            # Progress update
            processed = len(all_results)
            progress = processed / total_questions * 100
            avg_time_per_question = batch_time / len(batch)
            
            print(f"  ✅ Batch completed in {batch_time:.1f}s")
            print(f"  📈 Progress: {processed}/{total_questions} ({progress:.1f}%)")
            print(f"  ⚡ Speed: {avg_time_per_question:.2f}s per question")
            
            # Estimate remaining time
            remaining_questions = total_questions - processed
            estimated_remaining = remaining_questions * avg_time_per_question
            print(f"  ⏱️ Estimated remaining: {estimated_remaining/60:.1f} minutes")
        
        # Create submission DataFrame
        submission_data = []
        question_type_counts = {}
        
        for result in all_results:
            submission_data.append({
                'id': result.question_id,
                'answer': result.answer
            })
            
            # Count question types
            qtype = result.question_type
            question_type_counts[qtype] = question_type_counts.get(qtype, 0) + 1
        
        submission_df = pd.DataFrame(submission_data)
        
        # Save submission
        submission_df.to_csv(output_file, index=False)
        
        total_time = time.time() - start_time
        
        print("\n🎉 HIGH-PERFORMANCE PROCESSING COMPLETE!")
        print("=" * 60)
        print(f"✅ Submission saved: {output_file}")
        print(f"📊 Total questions: {total_questions}")
        print(f"⏱️ Total time: {total_time/60:.1f} minutes")
        print(f"⚡ Average speed: {total_time/total_questions:.2f}s per question")
        print(f"🔥 Throughput: {total_questions/(total_time/60):.1f} questions/minute")
        
        print(f"\n📈 Question Type Distribution:")
        for qtype, count in question_type_counts.items():
            percentage = count / total_questions * 100
            print(f"  {qtype}: {count} ({percentage:.1f}%)")
        
        # Calculate average confidence
        avg_confidence = np.mean([r.confidence for r in all_results])
        print(f"\n🎯 Average Confidence: {avg_confidence:.1%}")
        
        return submission_df

def main():
    """Main high-performance execution"""
    
    print("🚀 HIGH-PERFORMANCE FINANCIAL ANALYSIS COMPETITION")
    print("🔥 FULL GPU/CPU UTILIZATION MODE")
    print("=" * 70)
    
    # Initialize high-performance agent
    api_key = "sk-ArwixD6prvk3kCwlHbRb3Mjv9wMbByoaVrOXXQDnKnQqJBCV"
    
    # Use maximum available workers
    max_workers = min(64, (mp.cpu_count() or 1) * 4)  # Aggressive parallelization
    
    agent = HighPerformanceAgent(api_key, max_workers=max_workers)
    
    # Generate submission with full power
    submission = agent.generate_submission_fast(
        test_file="test.csv",
        output_file="high_performance_submission.csv",
        batch_size=100  # Larger batches for efficiency
    )
    
    print("\n🏆 COMPETITION SUBMISSION READY!")
    print("📁 File: high_performance_submission.csv")
    print("🎯 Optimized for maximum accuracy and speed!")

if __name__ == "__main__":
    main()
