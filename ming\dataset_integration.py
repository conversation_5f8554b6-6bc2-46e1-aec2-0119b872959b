"""
Dataset Integration Module for Financial Analysis Agent
Handles loading and preprocessing of:
- PowerInfer/LONGCOT-Refine-500K
- Josephgflowers/Finance-Instruct-500k
- airesearch/WangchanX-Legal-ThaiCCL-RAG (NEW: Legal knowledge for ethical financial analysis)
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from datasets import load_dataset, Dataset, DatasetDict
import pandas as pd
from transformers import AutoTokenizer
import torch
from torch.utils.data import DataLoader
import random
import re

logger = logging.getLogger(__name__)

class FinancialDatasetManager:
    """Manages financial datasets for training and fine-tuning"""
    
    def __init__(self, cache_dir: str = "./data_cache"):
        self.cache_dir = cache_dir
        self.datasets = {}
        self.processed_datasets = {}
        
        # Create cache directory
        os.makedirs(cache_dir, exist_ok=True)
        
    def load_longcot_dataset(self) -> Dataset:
        """Load PowerInfer/LONGCOT-Refine-500K dataset"""
        try:
            logger.info("Loading PowerInfer/LONGCOT-Refine-500K dataset...")
            dataset = load_dataset("PowerInfer/LONGCOT-Refine-500K", cache_dir=self.cache_dir)
            self.datasets['longcot'] = dataset
            logger.info(f"Loaded LONGCOT dataset with {len(dataset['train'])} examples")
            return dataset
        except Exception as e:
            logger.error(f"Failed to load LONGCOT dataset: {str(e)}")
            return None
    
    def load_finance_instruct_dataset(self) -> Dataset:
        """Load Josephgflowers/Finance-Instruct-500k dataset"""
        try:
            logger.info("Loading Josephgflowers/Finance-Instruct-500k dataset...")
            dataset = load_dataset("Josephgflowers/Finance-Instruct-500k", cache_dir=self.cache_dir)
            self.datasets['finance_instruct'] = dataset
            logger.info(f"Loaded Finance-Instruct dataset with {len(dataset['train'])} examples")
            return dataset
        except Exception as e:
            logger.error(f"Failed to load Finance-Instruct dataset: {str(e)}")
            return None

    def load_legal_rag_dataset(self) -> Dataset:
        """Load airesearch/WangchanX-Legal-ThaiCCL-RAG dataset for legal compliance"""
        try:
            logger.info("Loading airesearch/WangchanX-Legal-ThaiCCL-RAG dataset...")
            dataset = load_dataset("airesearch/WangchanX-Legal-ThaiCCL-RAG", cache_dir=self.cache_dir)
            self.datasets['legal_rag'] = dataset
            logger.info(f"Loaded Legal-RAG dataset with {len(dataset['train'])} examples")
            return dataset
        except Exception as e:
            logger.error(f"Failed to load Legal-RAG dataset: {str(e)}")
            return None
    
    def explore_dataset_structure(self, dataset_name: str) -> Dict[str, Any]:
        """Explore the structure of a loaded dataset"""
        if dataset_name not in self.datasets:
            logger.error(f"Dataset {dataset_name} not loaded")
            return {}
        
        dataset = self.datasets[dataset_name]
        
        # Get basic info
        info = {
            "dataset_name": dataset_name,
            "splits": list(dataset.keys()),
            "total_examples": sum(len(split) for split in dataset.values()),
        }
        
        # Analyze first few examples
        if 'train' in dataset:
            train_data = dataset['train']
            info["train_size"] = len(train_data)
            info["features"] = list(train_data.features.keys())
            
            # Sample a few examples
            sample_size = min(3, len(train_data))
            info["sample_examples"] = []
            for i in range(sample_size):
                example = train_data[i]
                info["sample_examples"].append({
                    "index": i,
                    "keys": list(example.keys()),
                    "example": {k: str(v)[:200] + "..." if len(str(v)) > 200 else str(v) 
                              for k, v in example.items()}
                })
        
        return info
    
    def preprocess_for_financial_analysis(self, dataset_name: str, 
                                        max_examples: Optional[int] = None) -> Dataset:
        """Preprocess dataset for financial analysis training"""
        if dataset_name not in self.datasets:
            logger.error(f"Dataset {dataset_name} not loaded")
            return None
        
        dataset = self.datasets[dataset_name]['train']
        
        if max_examples:
            dataset = dataset.select(range(min(max_examples, len(dataset))))
        
        # Process based on dataset type
        if dataset_name == 'longcot':
            processed = self._preprocess_longcot(dataset)
        elif dataset_name == 'finance_instruct':
            processed = self._preprocess_finance_instruct(dataset)
        elif dataset_name == 'legal_rag':
            processed = self._preprocess_legal_rag(dataset)
        else:
            logger.warning(f"No specific preprocessing for {dataset_name}")
            processed = dataset
        
        self.processed_datasets[dataset_name] = processed
        return processed
    
    def _preprocess_longcot(self, dataset: Dataset) -> Dataset:
        """Preprocess LONGCOT dataset for financial reasoning"""
        def process_example(example):
            # Extract relevant fields and format for financial analysis
            processed = {
                'instruction': example.get('instruction', ''),
                'input': example.get('input', ''),
                'output': example.get('output', ''),
                'reasoning_steps': example.get('reasoning_steps', []),
                'dataset_source': 'longcot'
            }
            
            # Create a formatted prompt for financial analysis
            if processed['instruction'] and processed['input']:
                processed['formatted_prompt'] = f"""
                Instruction: {processed['instruction']}
                Input: {processed['input']}
                
                Please provide a detailed financial analysis with step-by-step reasoning:
                """
            
            return processed
        
        return dataset.map(process_example)
    
    def _preprocess_finance_instruct(self, dataset: Dataset) -> Dataset:
        """Preprocess Finance-Instruct dataset"""
        def process_example(example):
            # Extract and format financial instruction data
            processed = {
                'instruction': example.get('instruction', ''),
                'input': example.get('input', ''),
                'output': example.get('output', ''),
                'dataset_source': 'finance_instruct'
            }
            
            # Add financial analysis context
            if processed['instruction']:
                processed['formatted_prompt'] = f"""
                Financial Analysis Task: {processed['instruction']}
                {f"Context: {processed['input']}" if processed['input'] else ""}
                
                Provide an ethical, transparent financial analysis:
                """
            
            return processed
        
        return dataset.map(process_example)

    def _preprocess_legal_rag(self, dataset: Dataset) -> Dataset:
        """Preprocess Legal-RAG dataset for financial compliance integration"""
        def process_example(example):
            # Extract legal question and context for financial compliance
            question = example.get('question', '')
            positive_contexts = example.get('positive_contexts', [])
            positive_answer = example.get('positive_answer', '')

            # Create financial compliance context
            legal_context = ""
            if positive_contexts:
                # Extract the most relevant legal context
                context_text = positive_contexts[0].get('context', '') if positive_contexts else ''
                legal_context = context_text[:500]  # Limit context length

            # Transform legal question into financial compliance format
            financial_question = self._transform_legal_to_financial(question)

            processed = {
                'instruction': financial_question,
                'input': legal_context,
                'output': self._adapt_legal_answer_for_finance(positive_answer, question),
                'legal_compliance': True,
                'original_legal_question': question,
                'dataset_source': 'legal_rag'
            }

            # Create formatted prompt for financial compliance analysis
            processed['formatted_prompt'] = f"""
            Financial Compliance Analysis: {financial_question}
            Legal Context: {legal_context}

            Provide an ethical financial analysis considering legal compliance:
            """

            return processed

        return dataset.map(process_example)

    def _transform_legal_to_financial(self, legal_question: str) -> str:
        """Transform legal questions into financial compliance questions"""
        # Map legal concepts to financial compliance scenarios
        financial_mappings = {
            'ธุรกิจสถาบันการเงิน': 'financial institution business compliance',
            'การถือหุ้น': 'shareholding regulations in financial analysis',
            'การลงทุน': 'investment compliance requirements',
            'การกำกับดูแล': 'regulatory oversight in financial decisions',
            'ความเสี่ยง': 'risk management and legal compliance',
            'การเปิดเผยข้อมูล': 'financial disclosure requirements',
            'กรรมการ': 'board governance in financial institutions',
            'เงินกองทุน': 'capital adequacy and regulatory compliance'
        }

        # Transform the question to focus on financial compliance
        transformed = legal_question
        for thai_term, english_concept in financial_mappings.items():
            if thai_term in legal_question:
                transformed = f"What are the {english_concept} considerations when {legal_question.lower()}?"
                break

        if transformed == legal_question:
            transformed = f"From a financial compliance perspective, how should we address: {legal_question}"

        return transformed

    def _adapt_legal_answer_for_finance(self, legal_answer: str, original_question: str) -> str:
        """Adapt legal answers for financial analysis context"""
        if not legal_answer:
            return "Legal compliance analysis required for this financial decision."

        # Add financial context to legal answer
        financial_context = f"""
        Financial Compliance Analysis:

        Legal Requirement: {legal_answer}

        Financial Implications:
        - This legal requirement must be considered in all financial analysis and recommendations
        - Investment decisions should comply with these regulatory standards
        - Risk assessment must include legal compliance factors
        - Ethical financial analysis requires adherence to these legal frameworks

        Recommendation: Ensure all financial strategies align with the stated legal requirements.
        """

        return financial_context

    def create_training_dataset(self, combine_datasets: bool = True,
                              train_split: float = 0.8) -> Tuple[Dataset, Dataset]:
        """Create training and validation datasets"""
        all_examples = []
        
        # Combine processed datasets
        for name, dataset in self.processed_datasets.items():
            logger.info(f"Adding {len(dataset)} examples from {name}")
            all_examples.extend(dataset)
        
        if not all_examples:
            logger.error("No processed datasets available")
            return None, None
        
        # Shuffle and split
        random.shuffle(all_examples)
        split_idx = int(len(all_examples) * train_split)
        
        train_examples = all_examples[:split_idx]
        val_examples = all_examples[split_idx:]
        
        # Convert to Dataset objects
        train_dataset = Dataset.from_list(train_examples)
        val_dataset = Dataset.from_list(val_examples)
        
        logger.info(f"Created training dataset: {len(train_dataset)} examples")
        logger.info(f"Created validation dataset: {len(val_dataset)} examples")
        
        return train_dataset, val_dataset
    
    def save_processed_datasets(self, output_dir: str = "./processed_datasets"):
        """Save processed datasets to disk"""
        os.makedirs(output_dir, exist_ok=True)
        
        for name, dataset in self.processed_datasets.items():
            output_path = os.path.join(output_dir, f"{name}_processed")
            dataset.save_to_disk(output_path)
            logger.info(f"Saved {name} dataset to {output_path}")
    
    def get_dataset_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics about loaded datasets"""
        stats = {
            "loaded_datasets": list(self.datasets.keys()),
            "processed_datasets": list(self.processed_datasets.keys()),
            "total_raw_examples": sum(len(ds['train']) for ds in self.datasets.values()),
            "total_processed_examples": sum(len(ds) for ds in self.processed_datasets.values()),
        }
        
        # Detailed stats per dataset
        for name, dataset in self.datasets.items():
            stats[f"{name}_raw"] = {
                "size": len(dataset['train']),
                "features": list(dataset['train'].features.keys())
            }
        
        for name, dataset in self.processed_datasets.items():
            stats[f"{name}_processed"] = {
                "size": len(dataset),
                "features": list(dataset.features.keys())
            }
        
        return stats

def demo_dataset_integration():
    """Demo function to test dataset integration"""
    print("🔄 Financial Dataset Integration Demo")
    print("=" * 50)
    
    # Initialize dataset manager
    manager = FinancialDatasetManager()
    
    # Load datasets
    print("Loading datasets...")
    longcot_ds = manager.load_longcot_dataset()
    finance_ds = manager.load_finance_instruct_dataset()
    legal_ds = manager.load_legal_rag_dataset()
    
    # Explore dataset structures
    if longcot_ds:
        print("\n📊 LONGCOT Dataset Structure:")
        longcot_info = manager.explore_dataset_structure('longcot')
        print(json.dumps(longcot_info, indent=2))
    
    if finance_ds:
        print("\n📊 Finance-Instruct Dataset Structure:")
        finance_info = manager.explore_dataset_structure('finance_instruct')
        print(json.dumps(finance_info, indent=2))

    if legal_ds:
        print("\n⚖️ Legal-RAG Dataset Structure:")
        legal_info = manager.explore_dataset_structure('legal_rag')
        print(json.dumps(legal_info, indent=2))

    # Preprocess datasets (using smaller samples for demo)
    print("\n🔧 Preprocessing datasets...")
    if longcot_ds:
        manager.preprocess_for_financial_analysis('longcot', max_examples=100)
    if finance_ds:
        manager.preprocess_for_financial_analysis('finance_instruct', max_examples=100)
    if legal_ds:
        manager.preprocess_for_financial_analysis('legal_rag', max_examples=100)
    
    # Create training datasets
    print("\n📚 Creating training datasets...")
    train_ds, val_ds = manager.create_training_dataset()
    
    if train_ds and val_ds:
        print(f"Training dataset: {len(train_ds)} examples")
        print(f"Validation dataset: {len(val_ds)} examples")
        
        # Show sample from training data
        print("\n📝 Sample training example:")
        sample = train_ds[0]
        for key, value in sample.items():
            print(f"{key}: {str(value)[:200]}{'...' if len(str(value)) > 200 else ''}")
    
    # Get statistics
    print("\n📈 Dataset Statistics:")
    stats = manager.get_dataset_statistics()
    print(json.dumps(stats, indent=2))
    
    return manager

if __name__ == "__main__":
    demo_dataset_integration()
