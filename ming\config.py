"""
Configuration file for Financial Analysis Agent
Contains API settings and model configurations
"""

# Typhoon API Configuration - CORRECTED ✅
TYPHOON_CONFIG = {
    # Primary API endpoint - OFFICIAL ENDPOINT
    "base_url": "https://api.opentyphoon.ai/v1",

    # Alternative endpoints to try
    "alternative_endpoints": [
        "https://api.opentyphoon.ai/v1",
        "https://api.together.ai/v1",
        "https://openrouter.ai/api/v1"
    ],

    # Model name - CONFIRMED WORKING
    "model_name": "typhoon-v2-70b-instruct",
    
    # API settings - OPTIMIZED FOR ACCURACY
    "timeout": 30,
    "max_tokens": 1024,  # Reduced to stay within limits
    "temperature": 0.1,  # Lower for more consistent answers
    
    # Retry settings
    "max_retries": 3,
    "retry_delay": 1.0
}

# Financial Analysis Settings
ANALYSIS_CONFIG = {
    "default_confidence_threshold": 0.6,
    "risk_free_rate": 0.02,  # 2% risk-free rate assumption
    "max_portfolio_size": 10,
    "default_time_horizon": 252,  # trading days in a year
}

# ESG Scoring Weights
ESG_WEIGHTS = {
    "environmental": 0.4,
    "social": 0.3,
    "governance": 0.3
}

# Risk Categories
RISK_CATEGORIES = {
    "conservative": {"max_volatility": 0.15, "max_beta": 0.8},
    "moderate": {"max_volatility": 0.25, "max_beta": 1.2},
    "aggressive": {"max_volatility": 1.0, "max_beta": 2.0}
}

# Explainability Settings
EXPLAINABILITY_CONFIG = {
    "min_explanation_components": 3,
    "required_transparency_score": 0.8,
    "ethical_compliance_threshold": 0.8
}

# Dataset Configuration
DATASET_CONFIG = {
    "cache_dir": "./data_cache",
    "max_examples_per_dataset": 1000,  # For demo purposes
    "preprocessing_batch_size": 100,
    "supported_datasets": [
        "PowerInfer/LONGCOT-Refine-500K",
        "Josephgflowers/Finance-Instruct-500k",
        "airesearch/WangchanX-Legal-ThaiCCL-RAG"
    ]
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "financial_agent.log"
}

def get_typhoon_config():
    """Get Typhoon API configuration"""
    return TYPHOON_CONFIG.copy()

def get_analysis_config():
    """Get analysis configuration"""
    return ANALYSIS_CONFIG.copy()

def update_typhoon_endpoint(new_endpoint: str):
    """Update the primary Typhoon API endpoint"""
    TYPHOON_CONFIG["base_url"] = new_endpoint

def update_api_key(new_key: str):
    """Update API key (note: this doesn't persist across sessions)"""
    # In production, this would be handled more securely
    pass
