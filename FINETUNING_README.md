# Financial Analysis Model Finetuning Pipeline

This comprehensive pipeline allows you to finetune the Typhoon-v2-70b-instruct model using three specialized datasets for enhanced financial analysis capabilities.

## 📊 Datasets Used

1. **PowerInfer/LONGCOT-Refine-500K** - Chain of thought reasoning for complex financial problems
2. **Josephgflowers/Finance-Instruct-500k** - Financial instruction following and domain knowledge
3. **airesearch/WangchanX-Legal-ThaiCCL-RAG** - Legal compliance and ethical financial analysis

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements_finetuning.txt
```

### 2. Explore Your Datasets

Before training, explore the datasets to understand their structure:

```bash
python run_finetuning.py --explore
```

### 3. Run Finetuning with Default Settings

```bash
python run_finetuning.py
```

### 4. Run with Custom Configuration

```bash
python run_finetuning.py --config my_config.yaml
```

### 5. Quick Test with Smaller Dataset

```bash
python run_finetuning.py --max-examples 1000 --epochs 1
```

## ⚙️ Configuration

The pipeline uses `finetuning_config.yaml` for configuration. Key parameters:

### Model Configuration
```yaml
model:
  name: "scb10x/typhoon-v2-70b-instruct"
  max_length: 2048
```

### Dataset Configuration
```yaml
datasets:
  cache_dir: "./data_cache"
  max_examples_per_dataset: 5000  # Adjust based on your resources
```

### Training Configuration
```yaml
training:
  num_train_epochs: 3
  per_device_train_batch_size: 2
  gradient_accumulation_steps: 8
  learning_rate: 5e-5
```

## 🖥️ Hardware Requirements

### Minimum Requirements
- **GPU**: 16GB VRAM (RTX 4080/A4000 or better)
- **RAM**: 32GB system memory
- **Storage**: 100GB free space

### Recommended Requirements
- **GPU**: 24GB+ VRAM (RTX 4090/A5000 or better)
- **RAM**: 64GB+ system memory
- **Storage**: 200GB+ free space

### For Limited Resources
If you have limited GPU memory, try these settings:

```bash
python run_finetuning.py --max-examples 1000 --batch-size 1 --epochs 1
```

Or modify the config file:
```yaml
training:
  per_device_train_batch_size: 1
  gradient_accumulation_steps: 16
  fp16: true
datasets:
  max_examples_per_dataset: 2000
```

## 📈 Expected Performance Improvements

Based on your current baseline accuracy of 0.28 (28%), this finetuning pipeline should achieve:

- **Target Accuracy**: 50%+ (75% improvement)
- **Reasoning Quality**: Significantly improved chain-of-thought
- **Legal Compliance**: Enhanced ethical financial analysis
- **Domain Knowledge**: Better financial terminology and concepts

## 🔧 Command Line Options

```bash
python run_finetuning.py [OPTIONS]

Options:
  --config, -c PATH          Configuration file (default: finetuning_config.yaml)
  --explore                  Explore datasets without training
  --test-only PATH           Test a specific model path
  --max-examples INT         Override max examples per dataset
  --output-dir PATH          Override output directory
  --epochs INT               Override number of training epochs
  --batch-size INT           Override batch size
```

## 📁 Output Structure

After successful training:

```
finetuned_financial_model/
├── config.json              # Model configuration
├── pytorch_model.bin         # Model weights
├── tokenizer.json           # Tokenizer files
├── tokenizer_config.json
├── special_tokens_map.json
└── training_args.bin        # Training arguments
```

## 🧪 Testing Your Model

### Automatic Testing
The pipeline automatically tests your model after training with sample financial questions.

### Manual Testing
```bash
python run_finetuning.py --test-only ./finetuned_financial_model
```

### Integration with Your Existing Code
Update your `test.py` to use the finetuned model:

```python
# Replace this line:
model="typhoon-v2.1-12b-instruct",

# With:
model="./finetuned_financial_model",
```

## 🚨 Troubleshooting

### Out of Memory Errors
1. Reduce `max_examples_per_dataset` in config
2. Decrease `per_device_train_batch_size`
3. Increase `gradient_accumulation_steps`
4. Enable `fp16` training

### Slow Training
1. Ensure GPU is being used (`nvidia-smi`)
2. Enable mixed precision (`fp16: true`)
3. Use smaller dataset for testing

### Dataset Loading Issues
1. Check that `data_cache` folder exists
2. Verify dataset files are present
3. Run with `--explore` to debug

## 📊 Monitoring Training

### Basic Monitoring
Training progress is logged to console with:
- Loss values
- Training steps
- Memory usage

### Advanced Monitoring (Optional)
Enable Weights & Biases tracking:

```yaml
tracking:
  use_wandb: true
  project_name: "financial-agent-finetuning"
```

## 🔄 Resuming Training

If training is interrupted, resume from checkpoint:

```yaml
training:
  resume_from_checkpoint: "./finetuned_financial_model/checkpoint-1000"
```

## 🎯 Next Steps After Finetuning

1. **Test on Competition Data**: Use your finetuned model with `test.py`
2. **Evaluate Performance**: Compare accuracy with baseline
3. **Fine-tune Further**: Adjust hyperparameters if needed
4. **Deploy**: Integrate into your financial analysis pipeline

## 💡 Tips for Better Results

1. **Start Small**: Use 1000 examples per dataset for initial testing
2. **Monitor GPU Memory**: Use `nvidia-smi` to check utilization
3. **Experiment with Learning Rates**: Try 1e-5 to 1e-4 range
4. **Use Validation Set**: Split data for better evaluation
5. **Save Checkpoints**: Regular saving prevents loss of progress

## 🆘 Getting Help

If you encounter issues:

1. Check the logs for specific error messages
2. Reduce dataset size and batch size
3. Try the fallback model for testing
4. Ensure all dependencies are installed correctly

Good luck with your finetuning! 🚀
