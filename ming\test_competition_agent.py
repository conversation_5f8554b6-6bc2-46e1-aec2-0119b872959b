"""
Test the Competition Agent with sample questions
"""

import pandas as pd
from competition_agent import CompetitionAgent

def test_sample_questions():
    """Test with a few sample questions"""
    
    print("🧪 Testing Competition Agent with Sample Questions")
    print("=" * 60)
    
    # Initialize agent
    api_key = "sk-RD2ySZFmAa5nP2WrwQfX4Q5ArZViFEkapK4UzS2jkLq8fErt"
    agent = CompetitionAgent(api_key)
    
    # Test multiple choice question
    print("\n📊 Test 1: Multiple Choice Question")
    print("-" * 40)
    
    mc_question = """Answer the question with the appropriate options A, B, C and D. Please respond with the exact answer A, B, C or D only. Do not be verbose or provide extra information. 
Question: What is the term for decisions limited by human capacity to absorb and analyse information?
Answer Choices: A: Cognitive rationality, B: Conscious rationality, C: Bounded rationality, D: Restricted rationality 
Answer:"""
    
    result1 = agent.analyze_multiple_choice(mc_question)
    print(f"Question Type: {result1.question_type}")
    print(f"Answer: {result1.extracted_answer}")
    print(f"Confidence: {result1.confidence:.1%}")
    
    # Test stock prediction question
    print("\n📈 Test 2: Stock Prediction Question")
    print("-" * 40)
    
    stock_question = """Assess the data and tweets to estimate whether the closing price of $msft will escalate or deflate at 2017-12-15. Respond with either Rise or Fall.
Context: date,open,high,low,close,adj-close,inc-5,inc-10,inc-15,inc-20,inc-25,inc-30
2017-12-01,-0.8,0.7,-1.2,0.1,0.1,-0.2,-0.8,-0.9,-0.8,-0.9,-1.9
2017-12-04,4.1,4.1,-0.5,-3.8,-3.8,3.0,2.9,2.8,2.9,2.9,2.1
2017-12-05,-0.3,1.3,-0.7,0.6,0.6,1.6,2.1,2.0,2.1,2.1,1.5
2017-12-06,-1.5,0.4,-1.6,1.5,1.5,-0.0,0.5,0.4,0.6,0.7,0.3
2017-12-07,0.1,0.4,-0.6,-0.4,-0.4,-0.1,0.8,0.8,0.9,1.0,0.8

2017-12-01: twitch is dominating the game streaming market $msft
2017-12-04: $msft microsoft co-founder bill gates giving $50 million for alzheimer's research
2017-12-05: forget china. india is where the growth is for big tech $msft
Answer:"""
    
    result2 = agent.analyze_stock_prediction(stock_question)
    print(f"Question Type: {result2.question_type}")
    print(f"Answer: {result2.extracted_answer}")
    print(f"Confidence: {result2.confidence:.1%}")
    
    # Test Thai compliance question
    print("\n🏛️ Test 3: Thai Compliance Question")
    print("-" * 40)
    
    compliance_question = """Read the questions and answers carefully, and choose the one you think is appropriate among the three options A, B and C.
Q: ABC Asset Management, a licensed asset management firm in Thailand, is onboarding a new institutional client, XYZ Charitable Foundation. During the Know Your Customer (KYC) process, the compliance officer, Ms. B, observes that the foundation receives regular large wire transfers from multiple overseas donors located in countries identified by the Financial Action Task Force (FATF) as having weak anti-money laundering and counter-terrorist financing controls. While the stated purpose of the foundation is humanitarian aid, some of the donors are listed on international watchlists for suspected ties to terrorist organizations. Under time pressure to close the deal, the relationship manager, Mr. C, suggests that since there is no direct evidence linking the specific transactions to terrorism, ABC Asset Management can proceed with onboarding the client, provided they strengthen ongoing transaction monitoring. Ms. B is unsure whether to escalate the matter or simply flag the account for enhanced monitoring. Which of the following is the MOST appropriate action for ABC Asset Management in this scenario, according to BOT's CFT guidelines and ethical obligations?

Options:
A. Escalate the case to senior management for their review and decision, but delay any reporting to AMLO until after a full internal investigation is completed.
B. Proceed with onboarding, but implement enhanced due diligence and ongoing transaction monitoring, reporting only if suspicious activity is detected after the account is opened.
C. Immediately file a suspicious transaction report (STR) with the Anti-Money Laundering Office (AMLO) and refuse to onboard XYZ Charitable Foundation until all concerns are resolved.
D. Onboard the client based on the foundation's stated purpose and the absence of direct evidence of terrorist financing, while documenting the decision for future reference."""
    
    result3 = agent.analyze_compliance_question(compliance_question)
    print(f"Question Type: {result3.question_type}")
    print(f"Answer: {result3.extracted_answer}")
    print(f"Confidence: {result3.confidence:.1%}")
    
    print("\n✅ Sample Testing Complete!")
    print("Agent is ready for full competition dataset processing.")

if __name__ == "__main__":
    test_sample_questions()
