# Finetuning Configuration for Financial Analysis Agent
# Adjust these parameters based on your hardware and requirements

# Model Configuration
model:
  name: "scb10x/typhoon-v2-70b-instruct" # Main model to finetune
  fallback_name: "microsoft/DialoGPT-medium" # Fallback for testing
  max_length: 2048 # Maximum sequence length
  torch_dtype: "float16" # Use float16 for GPU, float32 for CPU

# Dataset Configuration
datasets:
  cache_dir: "./data_cache"
  max_examples_per_dataset: 5000 # Adjust based on computational resources

  # Individual dataset limits (set to -1 for no limit)
  finance_instruct_limit: 10000
  longcot_limit: 8000
  legal_rag_limit: -1 # Use all legal examples (smaller dataset)

  # Data preprocessing
  shuffle_seed: 42
  train_test_split: 0.9 # 90% train, 10% validation

# Training Configuration
training:
  output_dir: "./finetuned_financial_model"
  overwrite_output_dir: true

  # Training hyperparameters
  num_train_epochs: 3
  per_device_train_batch_size: 2 # Small for large models
  per_device_eval_batch_size: 2
  gradient_accumulation_steps: 8 # Effective batch size = 2 * 8 = 16

  # Learning rate and optimization
  learning_rate: 5e-5
  weight_decay: 0.01
  adam_epsilon: 1e-8
  max_grad_norm: 1.0
  warmup_steps: 500
  lr_scheduler_type: "linear"

  # Logging and saving
  logging_steps: 100
  save_steps: 1000
  eval_steps: 1000
  save_total_limit: 3

  # Mixed precision and optimization
  fp16: true # Use mixed precision if GPU available
  dataloader_pin_memory: false
  remove_unused_columns: false
  prediction_loss_only: true

  # Evaluation
  evaluation_strategy: "steps"
  load_best_model_at_end: true
  metric_for_best_model: "eval_loss"

# Hardware Configuration
hardware:
  use_gpu: true
  device_map: "auto" # Automatic device mapping for multi-GPU
  low_cpu_mem_usage: true
  trust_remote_code: true

# Advanced Options
advanced:
  # Parameter-Efficient Fine-Tuning (LoRA)
  use_lora: false # Set to true to use LoRA instead of full fine-tuning
  lora_r: 16
  lora_alpha: 32
  lora_dropout: 0.1

  # Gradient checkpointing (saves memory)
  gradient_checkpointing: true

  # DeepSpeed configuration (for distributed training)
  use_deepspeed: false
  deepspeed_config: null

# Experiment Tracking
tracking:
  use_wandb: false # Set to true to enable Weights & Biases tracking
  project_name: "financial-agent-finetuning"
  experiment_name: "typhoon-v2-financial-analysis"
  tags: ["financial-analysis", "typhoon", "multi-dataset"]

# Testing Configuration
testing:
  test_after_training: true
  test_prompts:
    - |
      You are a financial analysis expert. Analyze the following scenario:

      A company has a current ratio of 2.5, debt-to-equity ratio of 0.4, and ROE of 15%. 
      The industry averages are: current ratio 2.0, debt-to-equity 0.6, and ROE 12%.

      What does this tell us about the company's financial health? Choose the best answer:
      A) The company is financially weak compared to industry
      B) The company shows strong liquidity and profitability 
      C) The company has excessive debt levels
      D) The company's performance is below industry standards

      Final answer:

    - |
      Explain the concept of options Greeks and their importance in risk management.
      Focus on Delta, Gamma, Theta, and Vega.

    - |
      A portfolio manager is considering ESG factors in investment decisions.
      What legal and ethical considerations should be taken into account?

# Resource Management
resources:
  # Memory management
  max_memory_per_gpu: "20GB" # Adjust based on your GPU memory
  cpu_memory_limit: "32GB" # Adjust based on your system RAM

  # Batch size optimization
  auto_find_batch_size: true # Automatically find optimal batch size

  # Checkpointing
  resume_from_checkpoint: null # Path to checkpoint to resume from
