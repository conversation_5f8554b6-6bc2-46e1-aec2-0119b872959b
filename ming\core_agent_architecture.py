"""
Core Agent Architecture for Financial Analysis Agent
Implements agentic workflows with explainable AI capabilities for ethical financial decision-making
"""

import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
import asyncio
from abc import ABC, abstractmethod

from financial_agent import TyphoonAPIClient, FinancialAnalysisResult

logger = logging.getLogger(__name__)

class AgentState(Enum):
    """States for the financial analysis agent"""
    IDLE = "idle"
    ANALYZING = "analyzing"
    RESEARCHING = "researching"
    REASONING = "reasoning"
    VALIDATING = "validating"
    REPORTING = "reporting"
    ERROR = "error"

class AnalysisType(Enum):
    """Types of financial analysis"""
    INVESTMENT_COMPARISON = "investment_comparison"
    RISK_ASSESSMENT = "risk_assessment"
    PORTFOLIO_OPTIMIZATION = "portfolio_optimization"
    ESG_ANALYSIS = "esg_analysis"
    MARKET_ANALYSIS = "market_analysis"
    ETHICAL_REVIEW = "ethical_review"

@dataclass
class AgentTask:
    """Represents a task for the financial agent"""
    task_id: str
    task_type: AnalysisType
    input_data: Dict[str, Any]
    priority: int = 1
    created_at: datetime = None
    status: str = "pending"
    result: Optional[FinancialAnalysisResult] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

class AgentTool(ABC):
    """Abstract base class for agent tools"""
    
    @abstractmethod
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the tool with given input data"""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """Get tool description for the agent"""
        pass

class MarketDataTool(AgentTool):
    """Tool for fetching market data and financial information"""
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate market data fetching"""
        symbol = input_data.get('symbol', 'UNKNOWN')
        
        # Simulate market data (in real implementation, use actual APIs)
        market_data = {
            'symbol': symbol,
            'current_price': 150.25,
            'change_percent': 2.5,
            'volume': 1000000,
            'market_cap': '2.5B',
            'pe_ratio': 18.5,
            'dividend_yield': 2.1,
            'beta': 1.2,
            'esg_score': 75,
            'timestamp': datetime.now().isoformat()
        }
        
        return {
            'status': 'success',
            'data': market_data,
            'source': 'market_data_api'
        }
    
    def get_description(self) -> str:
        return "Fetches real-time market data, financial metrics, and ESG scores for securities"

class RiskAssessmentTool(AgentTool):
    """Tool for risk assessment calculations"""
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform risk assessment"""
        investment_data = input_data.get('investment_data', {})
        
        # Simulate risk calculations
        risk_metrics = {
            'volatility': 0.25,
            'sharpe_ratio': 1.2,
            'max_drawdown': 0.15,
            'var_95': 0.08,
            'beta': investment_data.get('beta', 1.0),
            'risk_rating': 'Moderate',
            'risk_factors': [
                'Market volatility',
                'Sector concentration',
                'Liquidity risk'
            ]
        }
        
        return {
            'status': 'success',
            'risk_metrics': risk_metrics,
            'assessment_date': datetime.now().isoformat()
        }
    
    def get_description(self) -> str:
        return "Calculates comprehensive risk metrics including volatility, VaR, and risk ratings"

class ESGAnalysisTool(AgentTool):
    """Tool for ESG (Environmental, Social, Governance) analysis"""
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform ESG analysis"""
        company = input_data.get('company', 'Unknown')
        
        # Simulate ESG analysis
        esg_analysis = {
            'overall_score': 78,
            'environmental_score': 82,
            'social_score': 75,
            'governance_score': 77,
            'sustainability_rating': 'B+',
            'key_strengths': [
                'Strong environmental policies',
                'Diverse leadership',
                'Transparent reporting'
            ],
            'areas_for_improvement': [
                'Carbon footprint reduction',
                'Supply chain transparency'
            ],
            'compliance_status': 'Compliant'
        }
        
        return {
            'status': 'success',
            'esg_analysis': esg_analysis,
            'analysis_date': datetime.now().isoformat()
        }
    
    def get_description(self) -> str:
        return "Provides comprehensive ESG analysis including environmental, social, and governance metrics"

class FinancialAgentCore:
    """Core financial analysis agent with agentic workflows"""
    
    def __init__(self, api_key: str):
        self.typhoon_client = TyphoonAPIClient(api_key)
        self.state = AgentState.IDLE
        self.tools = {
            'market_data': MarketDataTool(),
            'risk_assessment': RiskAssessmentTool(),
            'esg_analysis': ESGAnalysisTool()
        }
        self.task_queue = []
        self.execution_history = []
        self.knowledge_base = {}
        
    async def add_task(self, task: AgentTask) -> str:
        """Add a task to the agent's queue"""
        self.task_queue.append(task)
        self.task_queue.sort(key=lambda x: x.priority, reverse=True)
        logger.info(f"Added task {task.task_id} of type {task.task_type}")
        return task.task_id
    
    async def process_next_task(self) -> Optional[FinancialAnalysisResult]:
        """Process the next task in the queue"""
        if not self.task_queue:
            return None
        
        task = self.task_queue.pop(0)
        logger.info(f"Processing task {task.task_id}")
        
        try:
            self.state = AgentState.ANALYZING
            result = await self._execute_task(task)
            task.result = result
            task.status = "completed"
            self.execution_history.append(task)
            self.state = AgentState.IDLE
            return result
            
        except Exception as e:
            logger.error(f"Error processing task {task.task_id}: {str(e)}")
            task.status = "failed"
            self.state = AgentState.ERROR
            return None
    
    async def _execute_task(self, task: AgentTask) -> FinancialAnalysisResult:
        """Execute a specific task using agentic workflow"""
        
        # Step 1: Research and gather data
        self.state = AgentState.RESEARCHING
        research_data = await self._research_phase(task)
        
        # Step 2: Reasoning and analysis
        self.state = AgentState.REASONING
        analysis_result = await self._reasoning_phase(task, research_data)
        
        # Step 3: Validation and ethical review
        self.state = AgentState.VALIDATING
        validated_result = await self._validation_phase(analysis_result)
        
        # Step 4: Generate final report
        self.state = AgentState.REPORTING
        final_result = await self._reporting_phase(validated_result, research_data)
        
        return final_result
    
    async def _research_phase(self, task: AgentTask) -> Dict[str, Any]:
        """Research phase: gather relevant data using tools"""
        research_data = {
            'task_id': task.task_id,
            'research_timestamp': datetime.now().isoformat(),
            'data_sources': []
        }
        
        # Determine which tools to use based on task type
        tools_to_use = self._select_tools_for_task(task.task_type)
        
        for tool_name in tools_to_use:
            if tool_name in self.tools:
                tool = self.tools[tool_name]
                try:
                    tool_result = await tool.execute(task.input_data)
                    research_data[tool_name] = tool_result
                    research_data['data_sources'].append(tool_name)
                except Exception as e:
                    logger.error(f"Tool {tool_name} failed: {str(e)}")
                    research_data[tool_name] = {'status': 'failed', 'error': str(e)}
        
        return research_data
    
    async def _reasoning_phase(self, task: AgentTask, research_data: Dict[str, Any]) -> FinancialAnalysisResult:
        """Reasoning phase: analyze data and generate insights"""
        
        # Create a comprehensive prompt for the LLM
        reasoning_prompt = self._create_reasoning_prompt(task, research_data)
        
        # Get analysis from Typhoon2
        llm_response = self.typhoon_client.query(reasoning_prompt)
        
        # Parse and structure the response
        result = self._parse_llm_response(llm_response, task, research_data)
        
        return result
    
    async def _validation_phase(self, result: FinancialAnalysisResult) -> FinancialAnalysisResult:
        """Validation phase: ethical review and fact-checking"""
        
        # Ethical validation prompt
        ethical_prompt = f"""
        Please review this financial analysis for ethical considerations:
        
        Recommendation: {result.recommendation}
        Reasoning: {result.reasoning}
        
        Check for:
        1. Potential conflicts of interest
        2. Bias in recommendations
        3. Compliance with ethical finance principles
        4. Transparency and explainability
        5. Potential harm to stakeholders
        
        Provide an ethical assessment and any necessary corrections.
        """
        
        ethical_review = self.typhoon_client.query(ethical_prompt)
        
        # Add ethical review to transparency report
        result.transparency_report['ethical_review'] = ethical_review
        result.transparency_report['validation_timestamp'] = datetime.now().isoformat()
        
        return result
    
    async def _reporting_phase(self, result: FinancialAnalysisResult, research_data: Dict[str, Any]) -> FinancialAnalysisResult:
        """Reporting phase: finalize and format the analysis"""
        
        # Add research data to transparency report
        result.transparency_report['research_data'] = research_data
        result.transparency_report['agent_state_history'] = [
            'researching', 'reasoning', 'validating', 'reporting'
        ]
        
        # Generate explanation
        explanation_prompt = f"""
        Create a clear, explainable summary of this financial analysis:
        
        Recommendation: {result.recommendation}
        Confidence: {result.confidence_score}
        Key Reasoning: {result.reasoning[:3] if result.reasoning else []}
        
        Explain in simple terms:
        1. What was analyzed
        2. Key factors considered
        3. Why this recommendation was made
        4. What risks were identified
        5. How confidence was determined
        
        Make it accessible to non-experts while maintaining accuracy.
        """
        
        explanation = self.typhoon_client.query(explanation_prompt)
        result.transparency_report['explanation'] = explanation
        
        return result
    
    def _select_tools_for_task(self, task_type: AnalysisType) -> List[str]:
        """Select appropriate tools based on task type"""
        tool_mapping = {
            AnalysisType.INVESTMENT_COMPARISON: ['market_data', 'risk_assessment', 'esg_analysis'],
            AnalysisType.RISK_ASSESSMENT: ['market_data', 'risk_assessment'],
            AnalysisType.ESG_ANALYSIS: ['esg_analysis', 'market_data'],
            AnalysisType.PORTFOLIO_OPTIMIZATION: ['market_data', 'risk_assessment'],
            AnalysisType.MARKET_ANALYSIS: ['market_data'],
            AnalysisType.ETHICAL_REVIEW: ['esg_analysis']
        }
        
        return tool_mapping.get(task_type, ['market_data'])
    
    def _create_reasoning_prompt(self, task: AgentTask, research_data: Dict[str, Any]) -> str:
        """Create a comprehensive reasoning prompt for the LLM"""
        
        prompt = f"""
        You are an expert financial analyst conducting a {task.task_type.value} analysis.
        
        Task Details:
        {json.dumps(task.input_data, indent=2)}
        
        Research Data:
        {json.dumps(research_data, indent=2, default=str)}
        
        Please provide a comprehensive financial analysis including:
        
        1. RECOMMENDATION: Clear, actionable recommendation
        2. CONFIDENCE: Confidence level (0-100%) with justification
        3. REASONING: Step-by-step analysis process (at least 5 key points)
        4. ETHICAL_CONSIDERATIONS: ESG factors and ethical implications
        5. RISK_ASSESSMENT: Key risks and mitigation strategies
        6. ASSUMPTIONS: Key assumptions made in the analysis
        7. LIMITATIONS: Limitations of the analysis and data
        
        Ensure your analysis is:
        - Transparent and explainable
        - Ethically sound
        - Based on the provided data
        - Actionable for decision-making
        """
        
        return prompt
    
    def _parse_llm_response(self, response: str, task: AgentTask, research_data: Dict[str, Any]) -> FinancialAnalysisResult:
        """Parse LLM response into structured result"""
        
        # Simple parsing (in production, use more sophisticated NLP)
        lines = response.split('\n')
        
        recommendation = "See detailed analysis"
        confidence_score = 0.75
        reasoning = []
        ethical_considerations = []
        risk_assessment = {}
        transparency_report = {
            'task_id': task.task_id,
            'analysis_type': task.task_type.value,
            'tools_used': research_data.get('data_sources', []),
            'llm_response': response
        }
        
        current_section = None
        for line in lines:
            line = line.strip()
            if "RECOMMENDATION:" in line.upper():
                current_section = "recommendation"
                recommendation = line.split(":", 1)[1].strip() if ":" in line else line
            elif "CONFIDENCE:" in line.upper():
                current_section = "confidence"
                # Extract percentage
                import re
                confidence_match = re.search(r'(\d+)%?', line)
                if confidence_match:
                    confidence_score = float(confidence_match.group(1)) / 100
            elif "REASONING:" in line.upper():
                current_section = "reasoning"
            elif "ETHICAL" in line.upper():
                current_section = "ethical"
            elif line and current_section:
                if current_section == "reasoning" and line.startswith(('1.', '2.', '3.', '4.', '5.', '-', '•')):
                    reasoning.append(line)
                elif current_section == "ethical" and line.startswith(('1.', '2.', '3.', '4.', '5.', '-', '•')):
                    ethical_considerations.append(line)
        
        return FinancialAnalysisResult(
            recommendation=recommendation,
            confidence_score=confidence_score,
            reasoning=reasoning,
            ethical_considerations=ethical_considerations,
            risk_assessment=risk_assessment,
            transparency_report=transparency_report,
            timestamp=datetime.now()
        )
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            'state': self.state.value,
            'queue_length': len(self.task_queue),
            'completed_tasks': len(self.execution_history),
            'available_tools': list(self.tools.keys()),
            'last_update': datetime.now().isoformat()
        }

async def demo_agent_architecture():
    """Demo the agent architecture"""
    print("🤖 Financial Agent Core Architecture Demo")
    print("=" * 50)
    
    # Initialize agent
    api_key = "sk-RD2ySZFmAa5nP2WrwQfX4Q5ArZViFEkapK4UzS2jkLq8fErt"
    agent = FinancialAgentCore(api_key)
    
    # Create sample tasks
    task1 = AgentTask(
        task_id="task_001",
        task_type=AnalysisType.INVESTMENT_COMPARISON,
        input_data={
            "option_a": "Tesla stock (TSLA)",
            "option_b": "Vanguard ESG fund (ESGV)",
            "investment_amount": 10000,
            "time_horizon": "5 years",
            "risk_tolerance": "moderate"
        },
        priority=1
    )
    
    task2 = AgentTask(
        task_id="task_002",
        task_type=AnalysisType.ESG_ANALYSIS,
        input_data={
            "company": "Apple Inc.",
            "symbol": "AAPL"
        },
        priority=2
    )
    
    # Add tasks to agent
    await agent.add_task(task1)
    await agent.add_task(task2)
    
    print(f"Agent Status: {agent.get_agent_status()}")
    
    # Process tasks
    print("\n🔄 Processing tasks...")
    result1 = await agent.process_next_task()
    if result1:
        print(f"\nTask 1 Result:")
        print(f"Recommendation: {result1.recommendation}")
        print(f"Confidence: {result1.confidence_score:.2%}")
        print(f"Tools used: {result1.transparency_report.get('tools_used', [])}")
    
    result2 = await agent.process_next_task()
    if result2:
        print(f"\nTask 2 Result:")
        print(f"Recommendation: {result2.recommendation}")
        print(f"Confidence: {result2.confidence_score:.2%}")
    
    print(f"\nFinal Agent Status: {agent.get_agent_status()}")

if __name__ == "__main__":
    asyncio.run(demo_agent_architecture())
