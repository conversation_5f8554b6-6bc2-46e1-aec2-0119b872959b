"""
Financial Analysis Modules
Specific financial analysis capabilities including investment strategies, risk assessment, and ethical considerations
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

@dataclass
class InvestmentOption:
    """Represents an investment option"""
    name: str
    symbol: str
    asset_type: str  # stock, bond, fund, crypto, etc.
    current_price: float
    expected_return: float
    volatility: float
    beta: float
    esg_score: Optional[float] = None
    market_cap: Optional[float] = None
    pe_ratio: Optional[float] = None
    dividend_yield: Optional[float] = None

@dataclass
class RiskMetrics:
    """Risk assessment metrics"""
    volatility: float
    var_95: float  # Value at Risk at 95% confidence
    var_99: float  # Value at Risk at 99% confidence
    sharpe_ratio: float
    max_drawdown: float
    beta: float
    risk_rating: str  # Conservative, Moderate, Aggressive
    risk_factors: List[str]

@dataclass
class PortfolioAllocation:
    """Portfolio allocation recommendation"""
    allocations: Dict[str, float]  # asset_name -> percentage
    expected_return: float
    expected_volatility: float
    sharpe_ratio: float
    diversification_score: float

class FinancialAnalysisModule(ABC):
    """Abstract base class for financial analysis modules"""
    
    @abstractmethod
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform the analysis"""
        pass
    
    @abstractmethod
    def get_module_info(self) -> Dict[str, str]:
        """Get module information"""
        pass

class InvestmentComparisonModule(FinancialAnalysisModule):
    """Module for comparing investment options"""
    
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Compare investment options"""
        options = data.get('options', [])
        user_profile = data.get('user_profile', {})
        
        if len(options) < 2:
            return {'error': 'Need at least 2 options to compare'}
        
        # Convert to InvestmentOption objects
        investment_options = []
        for opt in options:
            investment_options.append(InvestmentOption(**opt))
        
        # Perform comparison analysis
        comparison_results = self._compare_investments(investment_options, user_profile)
        
        return {
            'comparison_matrix': comparison_results['matrix'],
            'recommendation': comparison_results['recommendation'],
            'reasoning': comparison_results['reasoning'],
            'risk_analysis': comparison_results['risk_analysis'],
            'suitability_scores': comparison_results['suitability_scores']
        }
    
    def _compare_investments(self, options: List[InvestmentOption], user_profile: Dict[str, Any]) -> Dict[str, Any]:
        """Internal comparison logic"""
        
        # Create comparison matrix
        comparison_matrix = []
        for option in options:
            comparison_matrix.append({
                'name': option.name,
                'expected_return': option.expected_return,
                'volatility': option.volatility,
                'sharpe_ratio': option.expected_return / option.volatility if option.volatility > 0 else 0,
                'esg_score': option.esg_score or 0,
                'risk_rating': self._calculate_risk_rating(option.volatility),
                'suitability_score': self._calculate_suitability_score(option, user_profile)
            })
        
        # Determine recommendation
        best_option = max(comparison_matrix, key=lambda x: x['suitability_score'])
        
        reasoning = [
            f"Analyzed {len(options)} investment options",
            f"Considered user risk tolerance: {user_profile.get('risk_tolerance', 'moderate')}",
            f"Best option: {best_option['name']} with suitability score: {best_option['suitability_score']:.2f}",
            f"Key factors: return potential, risk level, ESG alignment"
        ]
        
        return {
            'matrix': comparison_matrix,
            'recommendation': best_option['name'],
            'reasoning': reasoning,
            'risk_analysis': self._analyze_risks(options),
            'suitability_scores': {opt['name']: opt['suitability_score'] for opt in comparison_matrix}
        }
    
    def _calculate_risk_rating(self, volatility: float) -> str:
        """Calculate risk rating based on volatility"""
        if volatility < 0.15:
            return "Conservative"
        elif volatility < 0.25:
            return "Moderate"
        else:
            return "Aggressive"
    
    def _calculate_suitability_score(self, option: InvestmentOption, user_profile: Dict[str, Any]) -> float:
        """Calculate how suitable an investment is for the user"""
        score = 0.0
        
        # Risk tolerance alignment
        user_risk = user_profile.get('risk_tolerance', 'moderate').lower()
        option_risk = self._calculate_risk_rating(option.volatility).lower()
        
        if user_risk == option_risk:
            score += 30
        elif abs(['conservative', 'moderate', 'aggressive'].index(user_risk) - 
                ['conservative', 'moderate', 'aggressive'].index(option_risk)) == 1:
            score += 15
        
        # Return potential (normalized)
        score += min(option.expected_return * 100, 30)
        
        # ESG alignment
        if user_profile.get('ethical_preferences') == 'ESG focused' and option.esg_score:
            score += option.esg_score * 0.4
        
        # Sharpe ratio
        sharpe = option.expected_return / option.volatility if option.volatility > 0 else 0
        score += min(sharpe * 10, 20)
        
        return min(score, 100)
    
    def _analyze_risks(self, options: List[InvestmentOption]) -> Dict[str, Any]:
        """Analyze risks across all options"""
        volatilities = [opt.volatility for opt in options]
        
        return {
            'average_volatility': np.mean(volatilities),
            'volatility_range': [min(volatilities), max(volatilities)],
            'diversification_benefit': len(set(opt.asset_type for opt in options)) > 1,
            'high_risk_options': [opt.name for opt in options if opt.volatility > 0.3]
        }
    
    def get_module_info(self) -> Dict[str, str]:
        return {
            'name': 'Investment Comparison Module',
            'description': 'Compares multiple investment options based on risk, return, and user preferences',
            'version': '1.0.0'
        }

class RiskAssessmentModule(FinancialAnalysisModule):
    """Module for comprehensive risk assessment"""
    
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform risk assessment"""
        investment_data = data.get('investment_data', {})
        portfolio_data = data.get('portfolio_data', [])
        time_horizon = data.get('time_horizon', 252)  # trading days in a year
        
        if investment_data:
            return self._assess_single_investment_risk(investment_data, time_horizon)
        elif portfolio_data:
            return self._assess_portfolio_risk(portfolio_data, time_horizon)
        else:
            return {'error': 'No investment or portfolio data provided'}
    
    def _assess_single_investment_risk(self, investment: Dict[str, Any], time_horizon: int) -> Dict[str, Any]:
        """Assess risk for a single investment"""
        
        # Extract key metrics
        volatility = investment.get('volatility', 0.2)
        expected_return = investment.get('expected_return', 0.08)
        beta = investment.get('beta', 1.0)
        
        # Calculate risk metrics
        risk_metrics = self._calculate_risk_metrics(expected_return, volatility, beta, time_horizon)
        
        # Risk factors analysis
        risk_factors = self._identify_risk_factors(investment)
        
        # Risk mitigation strategies
        mitigation_strategies = self._suggest_risk_mitigation(risk_metrics, investment)
        
        return {
            'risk_metrics': risk_metrics,
            'risk_factors': risk_factors,
            'mitigation_strategies': mitigation_strategies,
            'risk_summary': self._generate_risk_summary(risk_metrics)
        }
    
    def _calculate_risk_metrics(self, expected_return: float, volatility: float, beta: float, time_horizon: int) -> RiskMetrics:
        """Calculate comprehensive risk metrics"""
        
        # Sharpe ratio (assuming risk-free rate of 2%)
        risk_free_rate = 0.02
        sharpe_ratio = (expected_return - risk_free_rate) / volatility if volatility > 0 else 0
        
        # Value at Risk calculations
        var_95 = expected_return - 1.645 * volatility  # 95% confidence
        var_99 = expected_return - 2.326 * volatility  # 99% confidence
        
        # Maximum drawdown estimation
        max_drawdown = min(-0.05, -2 * volatility)
        
        # Risk rating
        if volatility < 0.15:
            risk_rating = "Conservative"
        elif volatility < 0.25:
            risk_rating = "Moderate"
        else:
            risk_rating = "Aggressive"
        
        # Risk factors
        risk_factors = []
        if volatility > 0.3:
            risk_factors.append("High volatility")
        if beta > 1.5:
            risk_factors.append("High market sensitivity")
        if expected_return > 0.15:
            risk_factors.append("High return expectations may indicate higher risk")
        
        return RiskMetrics(
            volatility=volatility,
            var_95=var_95,
            var_99=var_99,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            beta=beta,
            risk_rating=risk_rating,
            risk_factors=risk_factors
        )
    
    def _identify_risk_factors(self, investment: Dict[str, Any]) -> List[str]:
        """Identify specific risk factors"""
        factors = []
        
        asset_type = investment.get('asset_type', '').lower()
        
        if asset_type == 'stock':
            factors.extend(['Market risk', 'Company-specific risk', 'Sector risk'])
        elif asset_type == 'bond':
            factors.extend(['Interest rate risk', 'Credit risk', 'Inflation risk'])
        elif asset_type == 'crypto':
            factors.extend(['Extreme volatility', 'Regulatory risk', 'Technology risk'])
        elif asset_type == 'fund':
            factors.extend(['Management risk', 'Diversification risk', 'Fee impact'])
        
        # Market cap considerations
        market_cap = investment.get('market_cap', 0)
        if market_cap < 2e9:  # Small cap
            factors.append('Small-cap volatility risk')
        
        return factors
    
    def _suggest_risk_mitigation(self, risk_metrics: RiskMetrics, investment: Dict[str, Any]) -> List[str]:
        """Suggest risk mitigation strategies"""
        strategies = []
        
        if risk_metrics.volatility > 0.25:
            strategies.append("Consider position sizing to limit exposure")
            strategies.append("Implement stop-loss orders")
        
        if risk_metrics.beta > 1.3:
            strategies.append("Hedge with market-neutral positions")
            strategies.append("Consider defensive assets in portfolio")
        
        if risk_metrics.sharpe_ratio < 0.5:
            strategies.append("Evaluate if risk-adjusted returns justify the investment")
        
        strategies.append("Regular portfolio rebalancing")
        strategies.append("Diversification across asset classes")
        
        return strategies
    
    def _generate_risk_summary(self, risk_metrics: RiskMetrics) -> str:
        """Generate a human-readable risk summary"""
        return f"""
        Risk Assessment Summary:
        - Risk Level: {risk_metrics.risk_rating}
        - Volatility: {risk_metrics.volatility:.1%}
        - Sharpe Ratio: {risk_metrics.sharpe_ratio:.2f}
        - Potential Loss (95% confidence): {abs(risk_metrics.var_95):.1%}
        - Market Sensitivity (Beta): {risk_metrics.beta:.2f}
        """
    
    def get_module_info(self) -> Dict[str, str]:
        return {
            'name': 'Risk Assessment Module',
            'description': 'Comprehensive risk analysis including VaR, volatility, and risk factor identification',
            'version': '1.0.0'
        }

class ESGAnalysisModule(FinancialAnalysisModule):
    """Module for ESG (Environmental, Social, Governance) analysis"""
    
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform ESG analysis"""
        company_data = data.get('company_data', {})
        
        if not company_data:
            return {'error': 'No company data provided for ESG analysis'}
        
        esg_scores = self._calculate_esg_scores(company_data)
        sustainability_assessment = self._assess_sustainability(esg_scores)
        ethical_considerations = self._identify_ethical_considerations(company_data, esg_scores)
        
        return {
            'esg_scores': esg_scores,
            'sustainability_assessment': sustainability_assessment,
            'ethical_considerations': ethical_considerations,
            'investment_recommendation': self._generate_esg_recommendation(esg_scores)
        }
    
    def _calculate_esg_scores(self, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate ESG scores"""
        
        # Simulate ESG scoring (in real implementation, use actual ESG data providers)
        base_score = 50
        
        # Environmental factors
        environmental_score = base_score
        if company_data.get('renewable_energy_usage', 0) > 50:
            environmental_score += 20
        if company_data.get('carbon_neutral_commitment'):
            environmental_score += 15
        
        # Social factors
        social_score = base_score
        if company_data.get('diversity_score', 0) > 70:
            social_score += 15
        if company_data.get('employee_satisfaction', 0) > 80:
            social_score += 10
        
        # Governance factors
        governance_score = base_score
        if company_data.get('board_independence', 0) > 75:
            governance_score += 15
        if company_data.get('transparency_rating', 0) > 80:
            governance_score += 10
        
        overall_score = (environmental_score + social_score + governance_score) / 3
        
        return {
            'overall_score': min(overall_score, 100),
            'environmental_score': min(environmental_score, 100),
            'social_score': min(social_score, 100),
            'governance_score': min(governance_score, 100),
            'rating': self._score_to_rating(overall_score)
        }
    
    def _score_to_rating(self, score: float) -> str:
        """Convert numerical score to letter rating"""
        if score >= 90:
            return "AAA"
        elif score >= 80:
            return "AA"
        elif score >= 70:
            return "A"
        elif score >= 60:
            return "BBB"
        elif score >= 50:
            return "BB"
        else:
            return "B"
    
    def _assess_sustainability(self, esg_scores: Dict[str, Any]) -> Dict[str, Any]:
        """Assess long-term sustainability"""
        
        overall_score = esg_scores['overall_score']
        
        sustainability_level = "High" if overall_score >= 75 else "Medium" if overall_score >= 60 else "Low"
        
        return {
            'sustainability_level': sustainability_level,
            'long_term_viability': overall_score >= 70,
            'key_strengths': self._identify_esg_strengths(esg_scores),
            'improvement_areas': self._identify_improvement_areas(esg_scores)
        }
    
    def _identify_esg_strengths(self, esg_scores: Dict[str, Any]) -> List[str]:
        """Identify ESG strengths"""
        strengths = []
        
        if esg_scores['environmental_score'] >= 75:
            strengths.append("Strong environmental practices")
        if esg_scores['social_score'] >= 75:
            strengths.append("Excellent social responsibility")
        if esg_scores['governance_score'] >= 75:
            strengths.append("Robust governance structure")
        
        return strengths
    
    def _identify_improvement_areas(self, esg_scores: Dict[str, Any]) -> List[str]:
        """Identify areas for improvement"""
        improvements = []
        
        if esg_scores['environmental_score'] < 60:
            improvements.append("Environmental impact reduction needed")
        if esg_scores['social_score'] < 60:
            improvements.append("Social responsibility enhancement required")
        if esg_scores['governance_score'] < 60:
            improvements.append("Governance practices need strengthening")
        
        return improvements
    
    def _identify_ethical_considerations(self, company_data: Dict[str, Any], esg_scores: Dict[str, Any]) -> List[str]:
        """Identify ethical considerations for investment"""
        considerations = []
        
        # Industry-specific considerations
        industry = company_data.get('industry', '').lower()
        if 'tobacco' in industry or 'weapons' in industry:
            considerations.append("Industry may conflict with ESG investment principles")
        
        # Score-based considerations
        if esg_scores['overall_score'] < 50:
            considerations.append("Low ESG scores may indicate ethical concerns")
        
        # Governance red flags
        if company_data.get('board_independence', 100) < 50:
            considerations.append("Limited board independence raises governance concerns")
        
        return considerations
    
    def _generate_esg_recommendation(self, esg_scores: Dict[str, Any]) -> str:
        """Generate ESG-based investment recommendation"""
        score = esg_scores['overall_score']
        
        if score >= 80:
            return "Highly recommended for ESG-focused portfolios"
        elif score >= 65:
            return "Suitable for ESG investing with some considerations"
        elif score >= 50:
            return "Marginal ESG profile - consider alternatives"
        else:
            return "Not recommended for ESG-focused investing"
    
    def get_module_info(self) -> Dict[str, str]:
        return {
            'name': 'ESG Analysis Module',
            'description': 'Comprehensive ESG analysis for ethical investment decisions',
            'version': '1.0.0'
        }

def demo_financial_modules():
    """Demo the financial analysis modules"""
    print("💰 Financial Analysis Modules Demo")
    print("=" * 50)
    
    # Demo Investment Comparison
    print("\n📊 Investment Comparison Analysis:")
    comparison_module = InvestmentComparisonModule()
    
    comparison_data = {
        'options': [
            {
                'name': 'Tesla Stock',
                'symbol': 'TSLA',
                'asset_type': 'stock',
                'current_price': 250.0,
                'expected_return': 0.15,
                'volatility': 0.35,
                'beta': 1.8,
                'esg_score': 75
            },
            {
                'name': 'Vanguard ESG Fund',
                'symbol': 'ESGV',
                'asset_type': 'fund',
                'current_price': 65.0,
                'expected_return': 0.08,
                'volatility': 0.18,
                'beta': 0.95,
                'esg_score': 85
            }
        ],
        'user_profile': {
            'risk_tolerance': 'moderate',
            'ethical_preferences': 'ESG focused',
            'investment_horizon': '5 years'
        }
    }
    
    comparison_result = comparison_module.analyze(comparison_data)
    print(f"Recommendation: {comparison_result['recommendation']}")
    print(f"Reasoning: {comparison_result['reasoning']}")
    
    # Demo Risk Assessment
    print("\n⚠️ Risk Assessment Analysis:")
    risk_module = RiskAssessmentModule()
    
    risk_data = {
        'investment_data': {
            'name': 'High Growth Tech Stock',
            'volatility': 0.4,
            'expected_return': 0.18,
            'beta': 2.1,
            'asset_type': 'stock',
            'market_cap': 1.5e9
        }
    }
    
    risk_result = risk_module.analyze(risk_data)
    print(f"Risk Rating: {risk_result['risk_metrics'].risk_rating}")
    print(f"Risk Summary: {risk_result['risk_summary']}")
    
    # Demo ESG Analysis
    print("\n🌱 ESG Analysis:")
    esg_module = ESGAnalysisModule()
    
    esg_data = {
        'company_data': {
            'name': 'Sustainable Corp',
            'industry': 'renewable energy',
            'renewable_energy_usage': 80,
            'carbon_neutral_commitment': True,
            'diversity_score': 75,
            'employee_satisfaction': 85,
            'board_independence': 80,
            'transparency_rating': 90
        }
    }
    
    esg_result = esg_module.analyze(esg_data)
    print(f"ESG Rating: {esg_result['esg_scores']['rating']}")
    print(f"Overall Score: {esg_result['esg_scores']['overall_score']:.1f}")
    print(f"Investment Recommendation: {esg_result['investment_recommendation']}")

if __name__ == "__main__":
    demo_financial_modules()
