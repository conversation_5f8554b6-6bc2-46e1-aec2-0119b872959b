#!/usr/bin/env python3
"""
Debug script to isolate the training error
"""

import yaml
from transformers import TrainingArguments

def test_training_args():
    """Test creating TrainingArguments with our config"""
    
    # Load config
    with open('finetuning_config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    training_config = config.get('training', {})
    hardware_config = config.get('hardware', {})
    
    print("Training config values:")
    for key, value in training_config.items():
        print(f"  {key}: {value} (type: {type(value)})")
    
    print("\nHardware config values:")
    for key, value in hardware_config.items():
        print(f"  {key}: {value} (type: {type(value)})")
    
    try:
        # Create training args
        training_args = TrainingArguments(
            output_dir="./test_output",
            overwrite_output_dir=training_config.get('overwrite_output_dir', True),
            num_train_epochs=training_config.get('num_train_epochs', 3),
            per_device_train_batch_size=training_config.get('per_device_train_batch_size', 2),
            gradient_accumulation_steps=training_config.get('gradient_accumulation_steps', 8),
            warmup_steps=training_config.get('warmup_steps', 500),
            logging_steps=training_config.get('logging_steps', 100),
            save_steps=training_config.get('save_steps', 1000),
            save_total_limit=training_config.get('save_total_limit', 3),
            prediction_loss_only=training_config.get('prediction_loss_only', True),
            remove_unused_columns=training_config.get('remove_unused_columns', False),
            dataloader_pin_memory=training_config.get('dataloader_pin_memory', False),
            fp16=training_config.get('fp16', True) and hardware_config.get('use_gpu', True),
            learning_rate=training_config.get('learning_rate', 5e-5),
            weight_decay=training_config.get('weight_decay', 0.01),
            adam_epsilon=training_config.get('adam_epsilon', 1e-8),
            max_grad_norm=training_config.get('max_grad_norm', 1.0),
            lr_scheduler_type=training_config.get('lr_scheduler_type', 'linear'),
            report_to=None,
        )
        print("\n✅ TrainingArguments created successfully!")
        print(f"Learning rate: {training_args.learning_rate}")
        print(f"Max grad norm: {training_args.max_grad_norm}")
        
    except Exception as e:
        print(f"\n❌ Error creating TrainingArguments: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_training_args()
