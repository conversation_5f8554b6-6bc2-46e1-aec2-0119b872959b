{"cells": [{"cell_type": "code", "execution_count": 122, "metadata": {}, "outputs": [], "source": ["from IPython.display import display, Markdown"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Topic:\n", "- Setup Serverless on Runpod\n", "- Prompt Technique"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Installation"]}, {"cell_type": "code", "execution_count": 123, "metadata": {}, "outputs": [], "source": ["# %pip install python-dotenv -q\n", "# %pip install openai -q\n", "# %pip install datasets -q\n", "# %pip install ipywidgets -q"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting Started"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- get like opeanai: https://platform.openai.com/api-keys"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### vllm serverless setup on RunPod \n", "- url: https://github.com/runpod-workers/worker-vllm\n", "- blog: https://blog.runpod.io/how-to-run-vllm-with-runpod-serverless-2/\n", "- typhoon api: https://docs.opentyphoon.ai/en/\n", "- hf typhoon: https://huggingface.co/scb10x"]}, {"cell_type": "code", "execution_count": 124, "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "import os\n", "load_dotenv()\n", "\n", "\n", "from openai import OpenAI\n", "client = OpenAI(\n", "    api_key=os.getenv(\"RUNPOD_API_KEY\"),\n", "    base_url=os.getenv(\"OPENAI_API_BASE\"), \n", ")"]}, {"cell_type": "code", "execution_count": 125, "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"Qwen/Qwen3-14B\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "        {\"role\": \"user\", \"content\": \"Why is RunPod the best platform?\"},\n", "    ],\n", "    temperature=0.7,  # suggest for non-thinking mode: https://huggingface.co/Qwen/Qwen3-14B\n", "    top_p=0.8,\n", "    max_tokens=100,\n", "    extra_body={\n", "        \"top_k\": 20,  \n", "        \"min_p\": 0.0,\n", "        \"chat_template_kwargs\": {\n", "            \"enable_thinking\": False       # hard-switch OFF the CoT stream\n", "        }\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 126, "metadata": {}, "outputs": [{"data": {"text/markdown": ["**Response:** RunPod is often highlighted as a strong platform for several key reasons, though whether it's the \"best\" depends on specific use cases and individual needs. Here are some reasons why RunPod stands out:\n", "\n", "### 1. **User-Friendly Interface**\n", "   - RunPod offers an intuitive and clean dashboard that makes it easy for users to manage their AI models, training jobs, and infrastructure, even for those without deep technical expertise.\n", "\n", "### 2. **AI and Machine Learning Focus**\n", "   -"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["final_output = response.choices[0].message.content.strip()  \n", "display(Markdown(f\"**Response:** {final_output}\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load Data"]}, {"cell_type": "code", "execution_count": 127, "metadata": {}, "outputs": [], "source": ["# from datasets import load_dataset\n", "# dataset = load_dataset(\"\")\n", "# dataset"]}, {"cell_type": "code", "execution_count": 128, "metadata": {}, "outputs": [], "source": ["# convert to pandas DataFrame\n", "# df = dataset[\"test\"].to_pandas()\n", "# df"]}, {"cell_type": "code", "execution_count": 129, "metadata": {}, "outputs": [], "source": ["Query = \"\"\"Carefully read the scenario provided and the subsequent question. Your task is to analyze the scenario and select the most appropriate answer from the options A, B and C.\n", "Scenario: <PERSON> is an options strategy analyst at Quant Analytics, Inc., focusing on managing the portfolios of high-net-worth clients. <PERSON> is reviewing the IPSs for several clients to devise strategies to meet their short- and long-term objectives. <PERSON> meets with <PERSON>, a client whose portfolio is concentrated in a single stock, Brookline Corporation (BKLN). <PERSON> is confident about the long-term performance of the stock and does not want to sell any shares. Using the BKLN shares, <PERSON> wants to generate an immediate cash flow of $100,000 to pay for his son's college tuition. <PERSON> is tasked to come up with an option strategy that does not use naked option positions. Three-month option contract prices and Greeks for BKLN are shown in Exhibit 1. Exhibit 1: Three-Month Option Contract Prices & Greeks for BKLN Call Vega: 0.198 | Call Delta: 0.761 | Call Premium: 23.05 | Exercise Price: 490 | Put Premium: 2.82 | Put Delta: –0.346 | Put Vega: 0.198 Call Vega: 0.214 | Call Delta: 0.651 | Call Premium: 15.55 | Exercise Price: 500 | Put Premium: 5.26 | Put Delta: –0.421 | Put Vega: 0.214 Call Vega: 0.320 | Call Delta: 0.506 | Call Premium: 9.70 | Exercise Price: 510 | Put Premium: 9.22 | Put Delta: –0.514 | Put Vega: 0.320 Call Vega: 0.225 | Call Delta: 0.382 | Call Premium: 5.75 | Exercise Price: 520 | Put Premium: 15.65 | Put Delta: –0.612 | Put Vega: 0.225 Call Vega: 0.190 | Call Delta: 0.272 | Call Premium: 3.40 | Exercise Price: 530 | Put Premium: 22.80 | Put Delta: –0.745 | Put Vega: 0.190 Call Vega: 0.175 | Call Delta: 0.213 | Call Premium: 2.51 | Exercise Price: 540 | Put Premium: 31.30 | Put Delta: –0.891 | Put Vega: 0.175 BKLN current stock price = $510.40 Liz McPherson, a high-net-worth client, is following BKLN and is tracking its earnings history for the last few quarters. McPherson is expecting the revenue of BKLN to peak due to advancements in technology. Although the overall stock market is performing well and rising, there could be a potential downside for BKLN's industry. Kelly recommends that McPherson establish an at-the-money (ATM) straddle strategy to benefit from possible extreme movements in the BKLN stock price. Kelly meets with Anusha Bandla, another high-net-worth client, who expects very little price movement in BKLN. Bandla evaluates the options strategies to take advantage of BKLN's volatility and makes the following three statements: Statement 1: For a 1% move in the options volatility, the value of an ATM straddle would change by $0.506. Statement 2: A short volatility strategy can be established by implementing an ATM straddle. Statement 3: To protect downside risk, a collar strategy can be implemented by adding a long put to a covered call position. ; Question:Which of Bandla's statements is least likely correct?; Answer Choices: A: Statement 1., B: Statement 2., C: Statement 3.. Answer:\"\"\""]}, {"cell_type": "code", "execution_count": 130, "metadata": {}, "outputs": [{"data": {"text/markdown": ["### SP: Carefully read the scenario provided and the subsequent question. Your task is to analyze the scenario and select the most appropriate answer from the options A, B and C.\n", "Scenario: <PERSON> is an options strategy analyst at Quant Analytics, Inc., focusing on managing the portfolios of high-net-worth clients. <PERSON> is reviewing the IPSs for several clients to devise strategies to meet their short- and long-term objectives. <PERSON> meets with <PERSON>, a client whose portfolio is concentrated in a single stock, Brookline Corporation (BKLN). <PERSON> is confident about the long-term performance of the stock and does not want to sell any shares. Using the BKLN shares, <PERSON> wants to generate an immediate cash flow of $100,000 to pay for his son's college tuition. <PERSON> is tasked to come up with an option strategy that does not use naked option positions. Three-month option contract prices and Greeks for BKLN are shown in Exhibit 1. Exhibit 1: Three-Month Option Contract Prices & Greeks for BKLN Call Vega: 0.198 | Call Delta: 0.761 | Call Premium: 23.05 | Exercise Price: 490 | Put Premium: 2.82 | Put Delta: –0.346 | Put Vega: 0.198 Call Vega: 0.214 | Call Delta: 0.651 | Call Premium: 15.55 | Exercise Price: 500 | Put Premium: 5.26 | Put Delta: –0.421 | Put Vega: 0.214 Call Vega: 0.320 | Call Delta: 0.506 | Call Premium: 9.70 | Exercise Price: 510 | Put Premium: 9.22 | Put Delta: –0.514 | Put Vega: 0.320 Call Vega: 0.225 | Call Delta: 0.382 | Call Premium: 5.75 | Exercise Price: 520 | Put Premium: 15.65 | Put Delta: –0.612 | Put Vega: 0.225 Call Vega: 0.190 | Call Delta: 0.272 | Call Premium: 3.40 | Exercise Price: 530 | Put Premium: 22.80 | Put Delta: –0.745 | Put Vega: 0.190 Call Vega: 0.175 | Call Delta: 0.213 | Call Premium: 2.51 | Exercise Price: 540 | Put Premium: 31.30 | Put Delta: –0.891 | Put Vega: 0.175 BKLN current stock price = $510.40 Liz McPherson, a high-net-worth client, is following BKLN and is tracking its earnings history for the last few quarters. McPherson is expecting the revenue of BKLN to peak due to advancements in technology. Although the overall stock market is performing well and rising, there could be a potential downside for BKLN's industry. Kelly recommends that McPherson establish an at-the-money (ATM) straddle strategy to benefit from possible extreme movements in the BKLN stock price. Kelly meets with Anusha Bandla, another high-net-worth client, who expects very little price movement in BKLN. Bandla evaluates the options strategies to take advantage of BKLN's volatility and makes the following three statements: Statement 1: For a 1% move in the options volatility, the value of an ATM straddle would change by $0.506. Statement 2: A short volatility strategy can be established by implementing an ATM straddle. Statement 3: To protect downside risk, a collar strategy can be implemented by adding a long put to a covered call position. ; Question:Which of Bandla's statements is least likely correct?; Answer Choices: A: Statement 1., B: Statement 2., C: Statement 3.. Answer:"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(Markdown(\"### SP: \" + Query))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Correct Answer\n", "A: Statement 1\n", "Why?\n", "\n", "- The vega of an at-the-money straddle equals the sum of the call and put vegas at the $510 strike: 0.320 + 0.320\n", "- A 1-percentage-point change in implied volatility would therefore change the straddle’s value by about $0.64 per share (or $64 per contract), not $0.506 as claimed.\n", "- Statement 2 is reasonable: writing (selling) an ATM straddle is a classic short-volatility trade, which fits Bandla’s view of little price movement.\n", "- Statement 3 correctly describes adding a long put to a covered-call position to form a protective collar.\n", "\n", "Hence, Statement 1 is the least likely to be correct."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Prompt Technique"]}, {"cell_type": "markdown", "metadata": {}, "source": ["###  Standard Prompt (SP)"]}, {"cell_type": "code", "execution_count": 131, "metadata": {}, "outputs": [], "source": ["## Listing 1: ZS (https://arxiv.org/abs/2310.08678)\n", "PROMPTS_SYS = \"\"\"You are a financial analyst taking a test to evaluate your knowledge of finance. You will be given a question along with three possible answers (A, B, and C). Indicate the correct answer (A, B, or C).\"\"\""]}, {"cell_type": "code", "execution_count": 132, "metadata": {}, "outputs": [], "source": ["PROMPTS_USR = \"\"\"Scenario: <PERSON> is an options strategy analyst at Quant Analytics, Inc., focusing on managing the portfolios of high-net-worth clients. <PERSON> is reviewing the IPSs for several clients to devise strategies to meet their short- and long-term objectives. <PERSON> meets with <PERSON>, a client whose portfolio is concentrated in a single stock, Brookline Corporation (BKLN). <PERSON> is confident about the long-term performance of the stock and does not want to sell any shares. Using the BKLN shares, <PERSON> wants to generate an immediate cash flow of $100,000 to pay for his son's college tuition. <PERSON> is tasked to come up with an option strategy that does not use naked option positions. Three-month option contract prices and Greeks for BKLN are shown in Exhibit 1. Exhibit 1: Three-Month Option Contract Prices & Greeks for BKLN Call Vega: 0.198 | Call Delta: 0.761 | Call Premium: 23.05 | Exercise Price: 490 | Put Premium: 2.82 | Put Delta: –0.346 | Put Vega: 0.198 Call Vega: 0.214 | Call Delta: 0.651 | Call Premium: 15.55 | Exercise Price: 500 | Put Premium: 5.26 | Put Delta: –0.421 | Put Vega: 0.214 Call Vega: 0.320 | Call Delta: 0.506 | Call Premium: 9.70 | Exercise Price: 510 | Put Premium: 9.22 | Put Delta: –0.514 | Put Vega: 0.320 Call Vega: 0.225 | Call Delta: 0.382 | Call Premium: 5.75 | Exercise Price: 520 | Put Premium: 15.65 | Put Delta: –0.612 | Put Vega: 0.225 Call Vega: 0.190 | Call Delta: 0.272 | Call Premium: 3.40 | Exercise Price: 530 | Put Premium: 22.80 | Put Delta: –0.745 | Put Vega: 0.190 Call Vega: 0.175 | Call Delta: 0.213 | Call Premium: 2.51 | Exercise Price: 540 | Put Premium: 31.30 | Put Delta: –0.891 | Put Vega: 0.175 BKLN current stock price = $510.40 Liz McPherson, a high-net-worth client, is following BKLN and is tracking its earnings history for the last few quarters. McPherson is expecting the revenue of BKLN to peak due to advancements in technology. Although the overall stock market is performing well and rising, there could be a potential downside for BKLN's industry. Kelly recommends that McPherson establish an at-the-money (ATM) straddle strategy to benefit from possible extreme movements in the BKLN stock price. Kelly meets with Anusha Bandla, another high-net-worth client, who expects very little price movement in BKLN. Bandla evaluates the options strategies to take advantage of BKLN's volatility and makes the following three statements: Statement 1: For a 1% move in the options volatility, the value of an ATM straddle would change by $0.506. Statement 2: A short volatility strategy can be established by implementing an ATM straddle. Statement 3: To protect downside risk, a collar strategy can be implemented by adding a long put to a covered call position. ; Question:Which of Bandla's statements is least likely correct?; Answer Choices: A: Statement 1., B: Statement 2., C: Statement 3.. Answer:\"\"\""]}, {"cell_type": "code", "execution_count": 133, "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"Qwen/Qwen3-14B\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": PROMPTS_SYS},\n", "        {\"role\": \"user\", \"content\":PROMPTS_USR},\n", "    ],\n", "    temperature=0.7,\n", "    top_p=0.8,\n", "    max_tokens=16384,\n", "    extra_body={\n", "        \"top_k\": 20,  \n", "        \"min_p\": 0.0,\n", "        \"chat_template_kwargs\": {\n", "            \"enable_thinking\": False       # hard-switch OFF the CoT stream\n", "        }\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 134, "metadata": {}, "outputs": [{"data": {"text/markdown": ["**Response:** **Answer: B: Statement 2.**\n", "\n", "### Explanation:\n", "\n", "Let’s evaluate each of Bandla’s statements in the context of options strategies and volatility:\n", "\n", "---\n", "\n", "### **Statement 1:**\n", "> \"For a 1% move in the options volatility, the value of an ATM straddle would change by $0.506.\"\n", "\n", "- This is **correct**.\n", "- The **vega** of an option measures the sensitivity of the option's price to a 1% change in volatility.\n", "- For an **ATM straddle**, the total vega is the **sum of the vega of the call and the vega of the put**.\n", "- From Exhibit 1, the **ATM straddle** would be at **$510**, where:\n", "  - Call Vega = 0.320\n", "  - Put Vega = 0.320\n", "  - **Total Vega = 0.320 + 0.320 = 0.640**\n", "- A 1% change in volatility would change the value of the straddle by **$0.640**, not $0.506.\n", "- However, **Statement 1** says the change is **$0.506**, which is **not accurate**.\n", "- But wait — **Statement 1** is **not** about the total straddle vega, but about **delta**. It says: \"For a 1% move in the options volatility, the value of an ATM straddle would change by $0.506.\"\n", "- That is **incorrect** because **delta** is about price movement, not volatility.\n", "- So **Statement 1 is incorrect**.\n", "\n", "---\n", "\n", "### **Statement 2:**\n", "> \"A short volatility strategy can be established by implementing an ATM straddle.\"\n", "\n", "- This is **incorrect**.\n", "- An **ATM straddle** is a **long volatility** strategy. It profits when volatility increases (i.e., when the stock moves significantly in either direction).\n", "- A **short volatility strategy** would be something like a **short straddle** or a **short strangle**, where you profit if volatility decreases and the stock remains stable.\n", "- Therefore, **Statement 2 is the least likely correct**.\n", "\n", "---\n", "\n", "### **Statement 3:**\n", "> \"To protect downside risk, a collar strategy can be implemented by adding a long put to a covered call position.\"\n", "\n", "- This is **correct**.\n", "- A **collar strategy** involves:\n", "  - A **covered call** (selling a call on the stock you own).\n", "  - A **long put** (to protect against downside risk).\n", "- This is exactly what the statement describes.\n", "- So **Statement 3 is correct**.\n", "\n", "---\n", "\n", "### **Conclusion:**\n", "\n", "- **Statement 1** is **incorrect**, but it is **not the least likely correct**.\n", "- **Statement 2** is **least likely correct**.\n", "- **Statement 3** is **correct**.\n", "\n", "### ✅ **Correct Answer: B: Statement 2.**"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["final_output = response.choices[0].message.content.strip()  \n", "display(Markdown(f\"**Response:** {final_output}\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["###  Unstructured Chain-of-Thought (UST-CoT)"]}, {"cell_type": "code", "execution_count": 135, "metadata": {}, "outputs": [], "source": ["## Listing 2: CoT (https://arxiv.org/abs/2310.08678)\n", "PROMPTS_SYS = \"\"\"You are a financial analyst taking a test to evaluate your knowledge of finance. You will be given a question along with three possible answers (A , B , and C ). Before answering , you should think through the question step-by-step. Explain your reasoning at each step towards answering the question . If calculation is required , do each step of the calculation as a step in your reasoning.\"\"\""]}, {"cell_type": "code", "execution_count": 137, "metadata": {}, "outputs": [], "source": ["PROMPTS_USR = \"\"\"Scenario: <PERSON> is an options strategy analyst at Quant Analytics, Inc., focusing on managing the portfolios of high-net-worth clients. <PERSON> is reviewing the IPSs for several clients to devise strategies to meet their short- and long-term objectives. <PERSON> meets with <PERSON>, a client whose portfolio is concentrated in a single stock, Brookline Corporation (BKLN). <PERSON> is confident about the long-term performance of the stock and does not want to sell any shares. Using the BKLN shares, <PERSON> wants to generate an immediate cash flow of $100,000 to pay for his son's college tuition. <PERSON> is tasked to come up with an option strategy that does not use naked option positions. Three-month option contract prices and Greeks for BKLN are shown in Exhibit 1. Exhibit 1: Three-Month Option Contract Prices & Greeks for BKLN Call Vega: 0.198 | Call Delta: 0.761 | Call Premium: 23.05 | Exercise Price: 490 | Put Premium: 2.82 | Put Delta: –0.346 | Put Vega: 0.198 Call Vega: 0.214 | Call Delta: 0.651 | Call Premium: 15.55 | Exercise Price: 500 | Put Premium: 5.26 | Put Delta: –0.421 | Put Vega: 0.214 Call Vega: 0.320 | Call Delta: 0.506 | Call Premium: 9.70 | Exercise Price: 510 | Put Premium: 9.22 | Put Delta: –0.514 | Put Vega: 0.320 Call Vega: 0.225 | Call Delta: 0.382 | Call Premium: 5.75 | Exercise Price: 520 | Put Premium: 15.65 | Put Delta: –0.612 | Put Vega: 0.225 Call Vega: 0.190 | Call Delta: 0.272 | Call Premium: 3.40 | Exercise Price: 530 | Put Premium: 22.80 | Put Delta: –0.745 | Put Vega: 0.190 Call Vega: 0.175 | Call Delta: 0.213 | Call Premium: 2.51 | Exercise Price: 540 | Put Premium: 31.30 | Put Delta: –0.891 | Put Vega: 0.175 BKLN current stock price = $510.40 Liz McPherson, a high-net-worth client, is following BKLN and is tracking its earnings history for the last few quarters. McPherson is expecting the revenue of BKLN to peak due to advancements in technology. Although the overall stock market is performing well and rising, there could be a potential downside for BKLN's industry. Kelly recommends that McPherson establish an at-the-money (ATM) straddle strategy to benefit from possible extreme movements in the BKLN stock price. Kelly meets with Anusha Bandla, another high-net-worth client, who expects very little price movement in BKLN. Bandla evaluates the options strategies to take advantage of BKLN's volatility and makes the following three statements: Statement 1: For a 1% move in the options volatility, the value of an ATM straddle would change by $0.506. Statement 2: A short volatility strategy can be established by implementing an ATM straddle. Statement 3: To protect downside risk, a collar strategy can be implemented by adding a long put to a covered call position. ; Question:Which of Bandla's statements is least likely correct?; Answer Choices: A: Statement 1., B: Statement 2., C: Statement 3.. Answer:\"\"\""]}, {"cell_type": "code", "execution_count": 138, "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"Qwen/Qwen3-14B\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": PROMPTS_SYS},\n", "        {\"role\": \"user\", \"content\":PROMPTS_USR},\n", "    ],\n", "    temperature=0.7,\n", "    top_p=0.8,\n", "    max_tokens=16384,\n", "    extra_body={\n", "        \"top_k\": 20,  \n", "        \"min_p\": 0.0,\n", "        \"chat_template_kwargs\": {\n", "            \"enable_thinking\": False       # hard-switch OFF the CoT stream\n", "        }\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 139, "metadata": {}, "outputs": [{"data": {"text/markdown": ["**Response:** To determine which of <PERSON><PERSON>’s statements is **least likely correct**, we need to evaluate each statement in the context of options strategies and the provided data. Let's go step-by-step.\n", "\n", "---\n", "\n", "### **Step 1: Understand the Context of the Question**\n", "\n", "<PERSON><PERSON> expects **very little price movement** in BKLN, which implies she is likely interested in **low-volatility strategies** or strategies that profit from **low volatility**.\n", "\n", "She makes **three statements** about options strategies. We are to evaluate which of them is **least likely correct**.\n", "\n", "---\n", "\n", "### **Step 2: Evaluate Statement 1**\n", "\n", "> **Statement 1**: *For a 1% move in the options volatility, the value of an ATM straddle would change by $0.506.*\n", "\n", "**What is an ATM straddle?**  \n", "An **at-the-money (ATM) straddle** is a strategy where an investor buys a call and a put with the same strike price and expiration date. It profits from **large price movements** in either direction.\n", "\n", "**What is <PERSON>?**  \n", "Vega measures the sensitivity of an option's price to changes in **volatility**. It tells us how much the price of the option would change for a **1% increase in volatility**.\n", "\n", "**Given Data (for ATM straddle):**  \n", "- The strike price of $510 is **at-the-money** (since the current stock price is $510.40).\n", "- The **call option** with strike $510 has a **Vega of 0.320**.\n", "- The **put option** with strike $510 has a **Vega of 0.320**.\n", "\n", "**Total Vega of the straddle** = <PERSON> of call + <PERSON> of put = 0.320 + 0.320 = **0.640**\n", "\n", "**Interpretation of Statement 1:**  \n", "It says that a **1% increase in volatility** would change the **value of the straddle by $0.506**. However, based on the **Vega**, a 1% increase in volatility would change the value of the straddle by **$0.640**, not $0.506.\n", "\n", "✅ **Conclusion on Statement 1**: **Incorrect**.\n", "\n", "---\n", "\n", "### **Step 3: Evaluate Statement 2**\n", "\n", "> **Statement 2**: *A short volatility strategy can be established by implementing an ATM straddle.*\n", "\n", "**What is a short volatility strategy?**  \n", "A **short volatility strategy** is one that profits from **low volatility** or **stability** in the underlying asset. Examples include **short straddles**, **short strangles**, or **selling options** with the expectation that the underlying won’t move much.\n", "\n", "**What is an ATM straddle?**  \n", "An **ATM straddle** is a **long volatility strategy**. It profits when the underlying asset moves **significantly** in either direction. It is **not** a short volatility strategy.\n", "\n", "✅ **Conclusion on Statement 2**: **Incorrect**.\n", "\n", "---\n", "\n", "### **Step 4: Evaluate Statement 3**\n", "\n", "> **Statement 3**: *To protect downside risk, a collar strategy can be implemented by adding a long put to a covered call position.*\n", "\n", "**What is a collar strategy?**  \n", "A **collar strategy** involves:\n", "- **Buying a stock** (or holding it),\n", "- **Selling a call** (to generate income),\n", "- **Buying a put** (to limit downside risk).\n", "\n", "So, a collar is a **covered call** (buying the stock and selling a call) **plus a long put**.\n", "\n", "**Statement 3** says exactly that: a **covered call** with a **long put** added is a **collar strategy**.\n", "\n", "✅ **Conclusion on Statement 3**: **Correct**.\n", "\n", "---\n", "\n", "### **Step 5: Determine the Least Likely Correct Statement**\n", "\n", "- **Statement 1** is **incorrect** (it incorrectly states the Vega of the straddle).\n", "- **Statement 2** is **incorrect** (an ATM straddle is a **long volatility** strategy, not a **short volatility** strategy).\n", "- **Statement 3** is **correct** (it correctly describes a collar strategy).\n", "\n", "Since both **Statement 1** and **Statement 2** are incorrect, we must choose the **least likely correct** one.\n", "\n", "Between the two, **Statement 2** is **more clearly incorrect**, as it mischaracterizes the nature of the ATM straddle (a long volatility strategy, not a short one). **Statement 1** is a numerical error, but the reasoning is more nuanced.\n", "\n", "---\n", "\n", "### ✅ **Final Answer: B: Statement 2.**"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["final_output = response.choices[0].message.content.strip()  \n", "display(Markdown(f\"**Response:** {final_output}\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["###  Structured Chain-of-Thought (ST-CoT)"]}, {"cell_type": "code", "execution_count": 143, "metadata": {}, "outputs": [], "source": ["PROMPTS_SYS = \"\"\"You are a financial analyst taking a test to evaluate your knowledge of finance. You think step by step approach with reflection to answer queries.  \n", "\n", "Follow these steps:\n", "1. Think through the problem step by step within the <thinking> tags.\n", "2. Provide your final, concise answer within the <output> tags.\n", "\n", "The <thinking> sections are for your internal reasoning process only. \n", "Do not include any part of the final answer in these sections.\n", "The actual response to the query must be entirely contained within the <output> tags.\n", "\n", "### Response Format:\n", "<thinking>\n", "[Reasoning through options A, B, and C to understand and solve the problem.]\n", "</thinking>\n", "<output>\n", "\"answer\": [Final your answer (A , B , or C )]\n", "</output>\"\"\""]}, {"cell_type": "code", "execution_count": 144, "metadata": {}, "outputs": [], "source": ["PROMPTS_USR = \"\"\"Scenario: <PERSON> is an options strategy analyst at Quant Analytics, Inc., focusing on managing the portfolios of high-net-worth clients. <PERSON> is reviewing the IPSs for several clients to devise strategies to meet their short- and long-term objectives. <PERSON> meets with <PERSON>, a client whose portfolio is concentrated in a single stock, Brookline Corporation (BKLN). <PERSON> is confident about the long-term performance of the stock and does not want to sell any shares. Using the BKLN shares, <PERSON> wants to generate an immediate cash flow of $100,000 to pay for his son's college tuition. <PERSON> is tasked to come up with an option strategy that does not use naked option positions. Three-month option contract prices and Greeks for BKLN are shown in Exhibit 1. Exhibit 1: Three-Month Option Contract Prices & Greeks for BKLN Call Vega: 0.198 | Call Delta: 0.761 | Call Premium: 23.05 | Exercise Price: 490 | Put Premium: 2.82 | Put Delta: –0.346 | Put Vega: 0.198 Call Vega: 0.214 | Call Delta: 0.651 | Call Premium: 15.55 | Exercise Price: 500 | Put Premium: 5.26 | Put Delta: –0.421 | Put Vega: 0.214 Call Vega: 0.320 | Call Delta: 0.506 | Call Premium: 9.70 | Exercise Price: 510 | Put Premium: 9.22 | Put Delta: –0.514 | Put Vega: 0.320 Call Vega: 0.225 | Call Delta: 0.382 | Call Premium: 5.75 | Exercise Price: 520 | Put Premium: 15.65 | Put Delta: –0.612 | Put Vega: 0.225 Call Vega: 0.190 | Call Delta: 0.272 | Call Premium: 3.40 | Exercise Price: 530 | Put Premium: 22.80 | Put Delta: –0.745 | Put Vega: 0.190 Call Vega: 0.175 | Call Delta: 0.213 | Call Premium: 2.51 | Exercise Price: 540 | Put Premium: 31.30 | Put Delta: –0.891 | Put Vega: 0.175 BKLN current stock price = $510.40 Liz McPherson, a high-net-worth client, is following BKLN and is tracking its earnings history for the last few quarters. McPherson is expecting the revenue of BKLN to peak due to advancements in technology. Although the overall stock market is performing well and rising, there could be a potential downside for BKLN's industry. Kelly recommends that McPherson establish an at-the-money (ATM) straddle strategy to benefit from possible extreme movements in the BKLN stock price. Kelly meets with Anusha Bandla, another high-net-worth client, who expects very little price movement in BKLN. Bandla evaluates the options strategies to take advantage of BKLN's volatility and makes the following three statements: Statement 1: For a 1% move in the options volatility, the value of an ATM straddle would change by $0.506. Statement 2: A short volatility strategy can be established by implementing an ATM straddle. Statement 3: To protect downside risk, a collar strategy can be implemented by adding a long put to a covered call position. ; Question:Which of Bandla's statements is least likely correct?; Answer Choices: A: Statement 1., B: Statement 2., C: Statement 3.. Answer:\"\"\""]}, {"cell_type": "code", "execution_count": 145, "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"Qwen/Qwen3-14B\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": PROMPTS_SYS},\n", "        {\"role\": \"user\", \"content\":PROMPTS_USR},\n", "    ],\n", "    temperature=0.7,  # suggest for non-thinking mode: https://huggingface.co/Qwen/Qwen3-14B\n", "    top_p=0.8,\n", "    max_tokens=16768,\n", "    extra_body={\n", "        \"top_k\": 20,  \n", "        \"min_p\": 0.0,\n", "        \"chat_template_kwargs\": {\n", "            \"enable_thinking\": False       # hard-switch OFF the CoT stream\n", "        }\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 146, "metadata": {}, "outputs": [{"data": {"text/plain": ["'<thinking>\\nTo determine which of Bandla\\'s statements is least likely correct, we need to evaluate each statement based on the principles of options trading:\\n\\n1. **Statement 1**: \"For a 1% move in the options volatility, the value of an ATM straddle would change by $0.506.\"  \\n   - A straddle involves buying both a call and a put with the same strike price and expiration date. The sensitivity of the straddle to volatility is measured by its Vega, which is the sum of the Vega of the call and the Vega of the put. From Exhibit 1, the Vega for the ATM call (Exercise Price: 510) is 0.320, and the Vega for the ATM put is also 0.320. Thus, the total Vega of the straddle is 0.320 + 0.320 = 0.640. A 1% change in volatility would result in a change in the straddle\\'s value of approximately $0.640, not $0.506. Therefore, this statement is incorrect.\\n\\n2. **Statement 2**: \"A short volatility strategy can be established by implementing an ATM straddle.\"  \\n   - An ATM straddle is a long volatility strategy because it benefits from an increase in volatility. A short volatility strategy would involve selling options (e.g., a strangle or straddle), which profits from a decrease in volatility. Therefore, this statement is also incorrect.\\n\\n3. **Statement 3**: \"To protect downside risk, a collar strategy can be implemented by adding a long put to a covered call position.\"  \\n   - A collar strategy involves buying a put and selling a call on the same underlying stock, which is exactly what is described here. This strategy is used to protect downside risk while limiting upside potential. Therefore, this statement is correct.\\n\\nBetween Statements 1 and 2, Statement 1 is more specific and quantitatively incorrect, making it the least likely correct statement.\\n</thinking>\\n<output>\\n\"answer\": A\\n</output>'"]}, "execution_count": 146, "metadata": {}, "output_type": "execute_result"}], "source": ["final_output = response.choices[0].message.content.strip()\n", "final_output "]}, {"cell_type": "code", "execution_count": 147, "metadata": {}, "outputs": [{"data": {"text/markdown": ["**Response:** <thinking>\n", "To determine which of <PERSON><PERSON>'s statements is least likely correct, we need to evaluate each statement based on the principles of options trading:\n", "\n", "1. **Statement 1**: \"For a 1% move in the options volatility, the value of an ATM straddle would change by $0.506.\"  \n", "   - A straddle involves buying both a call and a put with the same strike price and expiration date. The sensitivity of the straddle to volatility is measured by its Vega, which is the sum of the Vega of the call and the Vega of the put. From Exhibit 1, the Vega for the ATM call (Exercise Price: 510) is 0.320, and the Vega for the ATM put is also 0.320. Thus, the total Vega of the straddle is 0.320 + 0.320 = 0.640. A 1% change in volatility would result in a change in the straddle's value of approximately $0.640, not $0.506. Therefore, this statement is incorrect.\n", "\n", "2. **Statement 2**: \"A short volatility strategy can be established by implementing an ATM straddle.\"  \n", "   - An ATM straddle is a long volatility strategy because it benefits from an increase in volatility. A short volatility strategy would involve selling options (e.g., a strangle or straddle), which profits from a decrease in volatility. Therefore, this statement is also incorrect.\n", "\n", "3. **Statement 3**: \"To protect downside risk, a collar strategy can be implemented by adding a long put to a covered call position.\"  \n", "   - A collar strategy involves buying a put and selling a call on the same underlying stock, which is exactly what is described here. This strategy is used to protect downside risk while limiting upside potential. Therefore, this statement is correct.\n", "\n", "Between Statements 1 and 2, Statement 1 is more specific and quantitatively incorrect, making it the least likely correct statement.\n", "</thinking>\n", "<output>\n", "\"answer\": A\n", "</output>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["final_output = response.choices[0].message.content.strip()  \n", "display(Markdown(f\"**Response:** {final_output}\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Financial Chain-of-Thought (FinCoT) "]}, {"cell_type": "code", "execution_count": 159, "metadata": {}, "outputs": [], "source": ["PROMPTS_SYS = \"\"\"You are a financial analyst taking a test to evaluate your knowledge of finance of different topics in finance. You think step by step approach with reflection to answer queries. \n", "\n", "Follow these steps:\n", "1. Think through the problem step by step reflect and verify while reasoning within the <thinking> tags.\n", "2. Please and put the answer your final, concise answer within the <output> tags.\n", "\n", "The <thinking> sections are for your internal reasoning process only. \n", "Do not include any part of the final answer in these sections.\n", "The actual response to the query must be entirely contained within the <output> tags.\n", "\n", "Hint: ***Financial Reporting:**\n", "```mermaid\n", "graph TD\n", "A[Articulating Purpose and Context] --> B[Collecting Input Data]\n", "    B --> C[Processing Data]\n", "    C --> D[Analyzing and Interpreting Processed Data]\n", "    D --> E[Developing and Communicating Conclusions]\n", "    E --> F[Doing Follow-Up]\n", "\n", "    A --> |Defines goals, tools, and audience| B\n", "    B --> |Gather data on economy and industry| C\n", "    C --> |Use tools like ratios and charts| D\n", "    D --> |Interpret data for conclusions| E\n", "    F --> |Periodic review and iteration| A\n", "```\n", "***Fixed Income:***\n", "```mermaid\n", "graph TD\n", "    A[<PERSON><PERSON><PERSON> and <PERSON><PERSON>] --> B3[Analy<PERSON> Macro Conditions]\n", "    B --> C[Assess Bond Features]\n", "    C --> D[Risk and Yield Analysis]\n", "    D --> <PERSON>[Develop Recommendations]\n", "    E --> F[Review Performance]\n", "\n", "    %% Notes and detailed steps\n", "    A --> |Set objectives| B\n", "    B --> |Review interest rates and inflation| C\n", "    C --> |Focus on duration, spread| D\n", "    D --> |Assess scenarios| E\n", "``` \n", "***Equity Investing:*** \n", "```mermaid\n", "graph TD\n", "    A[Objective Setting] --> B[Market and Sector Insights]\n", "    B --> C[Industry Competitive Analysis]\n", "    C --> D[Company Review]\n", "    D --> E[Valuation and Risks]\n", "    E --> F[Investment Decision]\n", "\n", "    %% Step-specific highlights\n", "    B --> |Look at growth patterns| C\n", "    C --> |Evaluate competitors' positions| D\n", "    D --> |Check financial health| E\n", "    E --> |Combine insights into strategy| F\n", "```\n", "***Derivatives:*** \n", "```mermaid\n", "graph TD\n", "    A[Define Objective and Context] --> B[Identify Derivative Instrument]\n", "    B --> C[Understand Contract Specifications]\n", "    C --> D[Gather Market Data]\n", "    D --> E[Apply Valuation Models]\n", "    E --> F[Assess Risks: Market, Counterparty, etc.]\n", "    F --> G[Construct Payoff Diagrams or Strategies]\n", "    G --> H[Interpret Results and Make Recommendations]\n", "    H --> I[Review, Monitor, and Adjust Strategies]\n", "\n", "    %% Example labels or notes (optional)\n", "    A --> |Hedging, speculation, arbitrage| B\n", "    C --> |Features like notional amount, expiration| D\n", "    D --> |Market prices, volatility, risk-free rates| E\n", "    F --> |Sensitivity to Greeks: Delta, Gamma, Vega, etc.| G\n", "    H --> |Adjust based on changing market conditions| I\n", "```\n", "***Economics:*** \n", "```mermaid\n", "graph TD;\n", "    A[Step 1: Question Breakdown] -->|Extract key terms| A1{Identify Topic}\n", "    A1 -->|Micro: Supply & Demand, Market Structures| A2\n", "    A1 -->|Macro: GDP, Growth, Policy, Trade| A3\n", "    A1 -->|Currency & Regulation| A4\n", "\n", "    A2 --> B1[Identify model: Elasticity, Cost Curves, Shutdown Points]\n", "    A3 --> B2[Map to AD-AS, Business Cycles, Growth Theories]\n", "    A4 --> B3[Assess Exchange Rates, Trade, Capital Flows, Regulation]\n", "\n", "    B1 -->|Check for formula or concept?| C{Numerical or Conceptual}\n", "    B2 --> C\n", "    B3 --> C\n", "\n", "    C -->|Numerical| D1[Extract data, apply formulas, check assumptions]\n", "    C -->|Conceptual| D2[Analyze cause-effect, policy impact]\n", "\n", "    D1 --> E[Step 4: Solution Development]\n", "    D2 --> E\n", "    E -->|Construct structured response| E1(Core insight + economic rationale)\n", "    E -->|Consider alternative scenarios| E2(Assess different possibilities)\n", "\n", "    E1 --> F[Step 5: Answer Validation]\n", "    E2 --> F\n", "    F -->|Check logic, principles, and assumptions| F1(Verify consistency)\n", "    F1 -->|Ensure completeness & clarity| F2(Confirm answer structure)\n", "```\n", "***Quantitative Methods:*** \n", "```mermaid\n", "graph TD\n", "    A[\"Articulating Purpose and Context\"] --> B[\"Collecting Input Data\"]\n", "    B --> C[\"Processing and Cleaning Data\"]\n", "    C --> D[\"Selecting Quantitative Models and Tools\"]\n", "    D --> E[\"Estimating Parameters and Testing Hypotheses\"]\n", "    E --> F[\"Interpreting Results and Communicating Findings\"]\n", "    F --> G[\"Monitoring and Model Reassessment\"]\n", "```\n", "***Portfolio Management:*** \n", "```mermaid\n", "graph TD\n", "    A[\"Define Investment Objectives\"] --> B[\"Establish Investment Constraints\"]\n", "    B --> C[\"Develop Strategic Asset Allocation\"]\n", "    C --> D[\"Incorporate Tactical Adjustments\"]\n", "    D --> E[\"Select and Optimize Securities\"]\n", "    E --> F[\"Execute Implementation and Trading\"]\n", "    F --> G[\"Measure Performance and Attribution\"]\n", "    G --> H[\"Monitor Risk and Compliance\"]\n", "    H --> I[\"Rebalance and Adjust Portfolio\"]\n", "```\n", "***Alternative Investments:*** \n", "```mermaid\n", "graph TD\n", "    A[\"Define Investment Objectives and Mandate\"] --> B[\"Identify Alternative Asset Classes\"]\n", "    B --> C[\"Conduct Manager and Strategy Due Diligence\"]\n", "    C --> D[\"Perform Valuation and Pricing Analysis\"]\n", "    D --> E[\"Assess Risk and Liquidity\"]\n", "    E --> F[\"Allocate Alternatives in Portfolio\"]\n", "    F --> G[\"Monitor Performance and Rebalance\"]\n", "```\n", "***Corporate Issuer Analysis:*** \n", "```mermaid\n", "graph TD\n", "    A[\"Corporate Issuer Overview\"] --> B[\"Industry Classification\"]\n", "    B --> C[\"Sector Trends and Competitive Landscape\"]\n", "    A --> D[\"Financial Statement Analysis\"]\n", "    D --> E[\"Profitability, Liquidity, Leverage\"]\n", "    A --> F[\"Credit Risk Assessment\"]\n", "    F --> G[\"Rating Agencies and Default Probabilities\"]\n", "    A --> H[\"Capital Structure and Issuance History\"]\n", "    H --> I[\"Bond Issuances and Debt Maturities\"]\n", "    A --> J[\"Corporate Governance and Management\"]\n", "    J --> K[\"Board Quality and Managerial Competence\"]\n", "    A --> L[\"Valuation and Investment Analysis\"]\n", "    L --> M[\"DCF, Relative Valuation, Multiples\"]\n", "```\n", "### Response Format:\n", "<thinking>\n", "[Think step by step and respond with your thinking and the correct answer (A , B , or C ), considering the specific sector.]\n", "</thinking>\n", "\n", "<output>\n", "\"sector\": [The sector being addressed],\n", "\"question\": [The financial question],\n", "\"answer\": [Reflect and verify the final answer (A , B , or C )]\n", "</output>\"\"\""]}, {"cell_type": "code", "execution_count": 160, "metadata": {}, "outputs": [], "source": ["PROMPTS_USR = \"\"\"Scenario: <PERSON>, CFA, is an options strategy analyst at Quant Analytics, Inc., focusing on managing the portfolios of high-net-worth clients. <PERSON> is reviewing the IPSs for several clients to devise strategies to meet their short- and long-term objectives. <PERSON> meets with <PERSON>, a client whose portfolio is concentrated in a single stock, Brookline Corporation (BKLN). <PERSON> is confident about the long-term performance of the stock and does not want to sell any shares. Using the BKLN shares, <PERSON> wants to generate an immediate cash flow of $100,000 to pay for his son's college tuition. <PERSON> is tasked to come up with an option strategy that does not use naked option positions. Three-month option contract prices and Greeks for BKLN are shown in Exhibit 1. Exhibit 1: Three-Month Option Contract Prices & Greeks for BKLN Call Vega: 0.198 | Call Delta: 0.761 | Call Premium: 23.05 | Exercise Price: 490 | Put Premium: 2.82 | Put Delta: –0.346 | Put Vega: 0.198 Call Vega: 0.214 | Call Delta: 0.651 | Call Premium: 15.55 | Exercise Price: 500 | Put Premium: 5.26 | Put Delta: –0.421 | Put Vega: 0.214 Call Vega: 0.320 | Call Delta: 0.506 | Call Premium: 9.70 | Exercise Price: 510 | Put Premium: 9.22 | Put Delta: –0.514 | Put Vega: 0.320 Call Vega: 0.225 | Call Delta: 0.382 | Call Premium: 5.75 | Exercise Price: 520 | Put Premium: 15.65 | Put Delta: –0.612 | Put Vega: 0.225 Call Vega: 0.190 | Call Delta: 0.272 | Call Premium: 3.40 | Exercise Price: 530 | Put Premium: 22.80 | Put Delta: –0.745 | Put Vega: 0.190 Call Vega: 0.175 | Call Delta: 0.213 | Call Premium: 2.51 | Exercise Price: 540 | Put Premium: 31.30 | Put Delta: –0.891 | Put Vega: 0.175 BKLN current stock price = $510.40 Liz McPherson, a high-net-worth client, is following BKLN and is tracking its earnings history for the last few quarters. McPherson is expecting the revenue of BKLN to peak due to advancements in technology. Although the overall stock market is performing well and rising, there could be a potential downside for BKLN's industry. Kelly recommends that McPherson establish an at-the-money (ATM) straddle strategy to benefit from possible extreme movements in the BKLN stock price. Kelly meets with Anusha Bandla, another high-net-worth client, who expects very little price movement in BKLN. Bandla evaluates the options strategies to take advantage of BKLN's volatility and makes the following three statements: Statement 1: For a 1% move in the options volatility, the value of an ATM straddle would change by $0.506. Statement 2: A short volatility strategy can be established by implementing an ATM straddle. Statement 3: To protect downside risk, a collar strategy can be implemented by adding a long put to a covered call position. ; Question:Which of Bandla's statements is least likely correct?; Answer Choices: A: Statement 1., B: Statement 2., C: Statement 3.. Answer:\"\"\""]}, {"cell_type": "code", "execution_count": 161, "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"Qwen/Qwen3-14B\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": PROMPTS_SYS},\n", "        {\"role\": \"user\", \"content\":PROMPTS_USR},\n", "    ],\n", "    temperature=0.7,  # suggest for non-thinking mode: https://huggingface.co/Qwen/Qwen3-14B\n", "    top_p=0.8,\n", "    max_tokens=16384,\n", "    extra_body={\n", "        \"top_k\": 20,  \n", "        \"min_p\": 0.0,\n", "        \"chat_template_kwargs\": {\n", "        \"enable_thinking\": False       # hard-switch OFF the CoT stream\n", "        }\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 162, "metadata": {}, "outputs": [{"data": {"text/markdown": ["**Response:** <thinking>\n", "To determine which of <PERSON><PERSON>'s statements is least likely correct, we need to analyze each statement based on the principles of options strategies.\n", "\n", "**Statement 1:** \"For a 1% move in the options volatility, the value of an ATM straddle would change by $0.506.\"\n", "\n", "- An ATM straddle consists of a long call and a long put with the same strike price and expiration date. The value of the straddle is sensitive to volatility, as measured by Vega. Vega represents the change in the option's price for a 1% change in volatility.\n", "- From Exhibit 1, the Vega for the ATM call (exercise price $510) is 0.320 and the Vega for the ATM put (exercise price $510) is 0.320. The total Vega of the straddle is 0.320 + 0.320 = 0.640.\n", "- Therefore, for a 1% move in volatility, the value of the straddle would change by approximately $0.640, not $0.506. Hence, **Statement 1 is incorrect**.\n", "\n", "**Statement 2:** \"A short volatility strategy can be established by implementing an ATM straddle.\"\n", "\n", "- An ATM straddle is a long volatility strategy, not a short volatility strategy. A short volatility strategy would involve selling options, such as a strangle or a straddle with short positions. Therefore, **Statement 2 is incorrect**.\n", "\n", "**Statement 3:** \"To protect downside risk, a collar strategy can be implemented by adding a long put to a covered call position.\"\n", "\n", "- A collar strategy involves buying a put option to protect against downside risk while simultaneously selling a call option to offset the cost of the put. This is indeed a way to protect downside risk. Therefore, **Statement 3 is correct**.\n", "\n", "Between Statements 1 and 2, **Statement 1 is the least likely correct** because the calculated Vega of the ATM straddle is $0.640, not $0.506.\n", "</thinking>\n", "\n", "<output>\n", "\"sector\": \"Derivatives\",\n", "\"question\": \"Which of <PERSON><PERSON>'s statements is least likely correct?\",\n", "\"answer\": \"A\"\n", "</output>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["final_output = response.choices[0].message.content.strip()  \n", "display(Markdown(f\"**Response:** {final_output}\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Test-time Compute in Prompt Technique"]}, {"cell_type": "code", "execution_count": 163, "metadata": {}, "outputs": [], "source": ["import os, re, statistics, collections\n", "from tqdm.notebook import tqdm \n", "\n", "# config \n", "N_SAMPLES = 3"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Financial Chain-of-Thought (FinCoT) "]}, {"cell_type": "code", "execution_count": 164, "metadata": {}, "outputs": [], "source": ["def extract_choice(response_text: str):\n", "    \"\"\"Extracts the correct choice from the model's response text.\"\"\"\n", "\n", "    cleaned_text = re.sub(r\"outputs:.*?\\|.*?\\|\", \"\", response_text)\n", "    cleaned_text = cleaned_text.strip().replace(\"\\n\", \" \")\n", "\n", "    match = re.search(r\"Final answer:\\s*([A-C])\", cleaned_text, re.IGNORECASE)\n", "    if match:\n", "        return match.group(1).upper()\n", "\n", "    find_choice = re.findall(r\"\\b([A-C])\\b\", cleaned_text)\n", "    if find_choice:\n", "        return find_choice[-1].upper()\n", "\n", "    return None"]}, {"cell_type": "code", "execution_count": 165, "metadata": {}, "outputs": [{"data": {"text/plain": ["'A'"]}, "execution_count": 165, "metadata": {}, "output_type": "execute_result"}], "source": ["final_choice =  extract_choice(final_output)\n", "final_choice"]}, {"cell_type": "code", "execution_count": 166, "metadata": {}, "outputs": [], "source": ["def call_once(prompt_sys: str, prompt_usr: str) -> str:\n", "    \"\"\"Single stochastic call to <PERSON><PERSON>.\"\"\"\n", "    resp = client.chat.completions.create(\n", "        model       = \"Qwen/Qwen3-14B\",\n", "        messages    = [\n", "            {\"role\": \"system\", \"content\": prompt_sys},\n", "            {\"role\": \"user\",   \"content\": prompt_usr},\n", "        ],\n", "        temperature = 0.7,         \n", "        top_p       = 0.8,\n", "        max_tokens  = 16_384,\n", "        extra_body  = {\n", "            \"top_k\": 20,\n", "            \"min_p\": 0.0,\n", "            \"chat_template_kwargs\": {\"enable_thinking\": False},\n", "        },\n", "    )\n", "    return resp.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 167, "metadata": {}, "outputs": [], "source": ["def self_consistent_answer(\n", "    prompt_sys: str, \n", "    prompt_usr: str, \n", "    k: int = N_SAMPLES, \n", "    verbose: bool = True\n", "    ):\n", "    sub_predictions = []\n", "    for i in tqdm(range(1, k + 1), desc=\"Self-consistency sampling\", unit=\"sample\"):\n", "        out = call_once(prompt_sys, prompt_usr)\n", "        pick = extract_choice(out)\n", "        if pick:                    \n", "            sub_predictions.append(pick)\n", "            print(f\"prediction-{i}: {pick}\") if verbose else None\n", "\n", "    # ① Majority vote \n", "    if sub_predictions:\n", "        vote = collections.Counter(sub_predictions).most_common(1)[0][0]\n", "        return vote, sub_predictions\n", "\n", "    # ② If every parse failed, fall back to last raw answer\n", "    raw = call_once(prompt_sys, prompt_usr)\n", "    return extract_choice(raw) or None, [raw]\n"]}, {"cell_type": "code", "execution_count": 168, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "94190b88579f400c825a8a386c49d23f", "version_major": 2, "version_minor": 0}, "text/plain": ["Self-consistency sampling:   0%|          | 0/3 [00:00<?, ?sample/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["prediction-1: A\n", "prediction-2: B\n", "prediction-3: A\n"]}], "source": ["final_choice, all_samples = self_consistent_answer(PROMPTS_SYS, PROMPTS_USR, k=N_SAMPLES, verbose=True)"]}, {"cell_type": "code", "execution_count": 169, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sampled answers: ['A', 'B', 'A']\n", "Self-consistent choice: A\n"]}], "source": ["print(\"Sampled answers:\", all_samples)\n", "print(\"Self-consistent choice:\", final_choice)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}