#!/usr/bin/env python3
"""
System Readiness Checker for Financial Model Finetuning
Checks hardware, software, and data requirements
"""

import os
import sys
import torch
import psutil
import shutil
from pathlib import Path
import subprocess
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_gpu():
    """Check GPU availability and memory"""
    logger.info("🔍 Checking GPU availability...")
    
    if not torch.cuda.is_available():
        logger.warning("⚠️ CUDA not available. Training will be very slow on CPU.")
        return False, 0
    
    gpu_count = torch.cuda.device_count()
    logger.info(f"✅ Found {gpu_count} GPU(s)")
    
    total_memory = 0
    for i in range(gpu_count):
        gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)  # GB
        gpu_name = torch.cuda.get_device_properties(i).name
        logger.info(f"   GPU {i}: {gpu_name} - {gpu_memory:.1f}GB")
        total_memory += gpu_memory
    
    if total_memory < 16:
        logger.warning("⚠️ Less than 16GB GPU memory. Consider using smaller batch sizes.")
    elif total_memory >= 24:
        logger.info("✅ Excellent GPU memory for finetuning!")
    else:
        logger.info("✅ Adequate GPU memory for finetuning.")
    
    return True, total_memory

def check_system_memory():
    """Check system RAM"""
    logger.info("🔍 Checking system memory...")
    
    memory = psutil.virtual_memory()
    total_gb = memory.total / (1024**3)
    available_gb = memory.available / (1024**3)
    
    logger.info(f"   Total RAM: {total_gb:.1f}GB")
    logger.info(f"   Available RAM: {available_gb:.1f}GB")
    
    if total_gb < 16:
        logger.warning("⚠️ Less than 16GB RAM. May cause issues with large datasets.")
        return False
    elif total_gb >= 32:
        logger.info("✅ Excellent system memory!")
        return True
    else:
        logger.info("✅ Adequate system memory.")
        return True

def check_disk_space():
    """Check available disk space"""
    logger.info("🔍 Checking disk space...")
    
    current_dir = Path.cwd()
    disk_usage = shutil.disk_usage(current_dir)
    free_gb = disk_usage.free / (1024**3)
    
    logger.info(f"   Free disk space: {free_gb:.1f}GB")
    
    if free_gb < 50:
        logger.warning("⚠️ Less than 50GB free space. May not be enough for model and datasets.")
        return False
    elif free_gb >= 200:
        logger.info("✅ Excellent disk space!")
        return True
    else:
        logger.info("✅ Adequate disk space.")
        return True

def check_datasets():
    """Check if datasets are available"""
    logger.info("🔍 Checking datasets...")
    
    data_cache = Path("./data_cache")
    if not data_cache.exists():
        logger.error("❌ data_cache folder not found!")
        return False
    
    datasets = [
        "Josephgflowers___finance-instruct-500k",
        "PowerInfer___longcot-refine-500_k", 
        "airesearch___wangchan_x-legal-thai_ccl-rag"
    ]
    
    found_datasets = 0
    for dataset in datasets:
        dataset_path = data_cache / dataset
        if dataset_path.exists():
            logger.info(f"   ✅ Found {dataset}")
            found_datasets += 1
        else:
            logger.warning(f"   ⚠️ Missing {dataset}")
    
    if found_datasets == 0:
        logger.error("❌ No datasets found in data_cache!")
        return False
    elif found_datasets < 3:
        logger.warning(f"⚠️ Only {found_datasets}/3 datasets found. Training will proceed with available datasets.")
        return True
    else:
        logger.info("✅ All datasets found!")
        return True

def check_python_packages():
    """Check required Python packages"""
    logger.info("🔍 Checking Python packages...")
    
    required_packages = [
        "torch", "transformers", "datasets", "numpy", "pandas", 
        "tqdm", "pathlib", "yaml", "accelerate"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"   ✅ {package}")
        except ImportError:
            logger.warning(f"   ❌ {package} not found")
            missing_packages.append(package)
    
    if missing_packages:
        logger.warning(f"⚠️ Missing packages: {', '.join(missing_packages)}")
        logger.info("💡 Install with: pip install -r requirements_finetuning.txt")
        return False
    else:
        logger.info("✅ All required packages found!")
        return True

def check_pytorch_version():
    """Check PyTorch version compatibility"""
    logger.info("🔍 Checking PyTorch version...")
    
    torch_version = torch.__version__
    logger.info(f"   PyTorch version: {torch_version}")
    
    # Check if version is recent enough
    major, minor = torch_version.split('.')[:2]
    if int(major) >= 2 or (int(major) == 1 and int(minor) >= 12):
        logger.info("✅ PyTorch version is compatible!")
        return True
    else:
        logger.warning("⚠️ PyTorch version may be too old. Consider upgrading to 2.0+")
        return False

def estimate_training_time(gpu_memory_gb: float, dataset_size: int):
    """Estimate training time based on hardware"""
    logger.info("🔍 Estimating training time...")
    
    # Rough estimates based on typical performance
    if gpu_memory_gb >= 24:
        # High-end GPU
        time_per_1k_examples = 10  # minutes
        speed_category = "Fast"
    elif gpu_memory_gb >= 16:
        # Mid-range GPU
        time_per_1k_examples = 20  # minutes
        speed_category = "Moderate"
    elif gpu_memory_gb >= 8:
        # Lower-end GPU
        time_per_1k_examples = 40  # minutes
        speed_category = "Slow"
    else:
        # CPU or very limited GPU
        time_per_1k_examples = 120  # minutes
        speed_category = "Very Slow"
    
    estimated_hours = (dataset_size / 1000) * time_per_1k_examples / 60
    
    logger.info(f"   Training speed category: {speed_category}")
    logger.info(f"   Estimated training time: {estimated_hours:.1f} hours")
    
    if estimated_hours > 24:
        logger.warning("⚠️ Training may take more than 24 hours. Consider reducing dataset size.")
    elif estimated_hours > 8:
        logger.info("💡 Training will take several hours. Consider running overnight.")
    else:
        logger.info("✅ Training should complete in reasonable time.")

def provide_recommendations(gpu_available: bool, gpu_memory: float):
    """Provide configuration recommendations"""
    logger.info("💡 Configuration Recommendations:")
    
    if not gpu_available:
        logger.info("   - Use CPU-only configuration")
        logger.info("   - Set max_examples_per_dataset to 500-1000")
        logger.info("   - Use batch_size of 1")
        logger.info("   - Consider using a smaller model for testing")
    elif gpu_memory < 16:
        logger.info("   - Use small batch sizes (1-2)")
        logger.info("   - Enable gradient checkpointing")
        logger.info("   - Set max_examples_per_dataset to 2000-3000")
        logger.info("   - Use fp16 precision")
    elif gpu_memory < 24:
        logger.info("   - Use moderate batch sizes (2-4)")
        logger.info("   - Set max_examples_per_dataset to 5000-8000")
        logger.info("   - Use fp16 precision")
    else:
        logger.info("   - Can use larger batch sizes (4-8)")
        logger.info("   - Set max_examples_per_dataset to 10000+")
        logger.info("   - Consider using full precision if needed")

def main():
    """Main system check"""
    logger.info("🚀 Financial Model Finetuning - System Readiness Check")
    logger.info("=" * 60)
    
    checks_passed = 0
    total_checks = 6
    
    # Run all checks
    gpu_available, gpu_memory = check_gpu()
    if gpu_available: checks_passed += 1
    
    if check_system_memory(): checks_passed += 1
    if check_disk_space(): checks_passed += 1
    if check_datasets(): checks_passed += 1
    if check_python_packages(): checks_passed += 1
    if check_pytorch_version(): checks_passed += 1
    
    # Estimate training time
    estimate_training_time(gpu_memory, 15000)  # Assume 15k examples total
    
    # Provide recommendations
    provide_recommendations(gpu_available, gpu_memory)
    
    # Final assessment
    logger.info("=" * 60)
    logger.info(f"📊 System Readiness: {checks_passed}/{total_checks} checks passed")
    
    if checks_passed >= 5:
        logger.info("🎉 System is ready for finetuning!")
        logger.info("🚀 You can proceed with: python run_finetuning.py")
    elif checks_passed >= 3:
        logger.info("⚠️ System has some limitations but can proceed with caution.")
        logger.info("💡 Consider starting with smaller datasets and batch sizes.")
    else:
        logger.warning("❌ System may not be ready for finetuning.")
        logger.info("🔧 Please address the issues above before proceeding.")

if __name__ == "__main__":
    main()
