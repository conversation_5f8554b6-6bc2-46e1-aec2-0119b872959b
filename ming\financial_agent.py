"""
Financial Analysis Agent for International Online Hackathon 2025
Explainable AI-powered Ethical Financial Analysis Agent using Typhoon2 LLM
Enhanced with Legal Compliance and Regulatory Awareness
"""

import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
from typhoon_client import TyphoonAP<PERSON>lient
from advanced_prompting_engine import AdvancedPromptingEngine, PromptingTechnique
from legal_financial_fusion import LegalFinancialFusion

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class FinancialAnalysisResult:
    """Structure for financial analysis results with explainability and legal compliance"""
    recommendation: str
    confidence_score: float
    reasoning: List[str]
    ethical_considerations: List[str]
    risk_assessment: Dict[str, Any]
    transparency_report: Dict[str, Any]
    legal_compliance: Dict[str, Any]  # NEW: Legal compliance assessment
    regulatory_warnings: List[str]    # NEW: Regulatory warnings and alerts
    compliance_score: float          # NEW: Compliance score (0-1)
    timestamp: datetime



class FinancialAnalysisAgent:
    """Main Financial Analysis Agent with explainable AI and legal compliance capabilities"""

    def __init__(self, api_key: str, use_advanced_prompting: bool = True, enable_legal_compliance: bool = True):
        self.typhoon_client = TyphoonAPIClient(api_key)
        self.analysis_history = []
        self.use_advanced_prompting = use_advanced_prompting
        self.enable_legal_compliance = enable_legal_compliance

        if use_advanced_prompting:
            self.advanced_prompting = AdvancedPromptingEngine(api_key)
        else:
            self.advanced_prompting = None

        # Initialize legal compliance module
        if enable_legal_compliance:
            self.legal_fusion = LegalFinancialFusion()
        else:
            self.legal_fusion = None
    
    def analyze_investment_comparison(self, option_a: str, option_b: str,
                                   user_profile: Dict[str, Any] = None,
                                   prompting_technique: PromptingTechnique = PromptingTechnique.FINANCIAL_COT) -> FinancialAnalysisResult:
        """Compare two investment options with explainable reasoning"""

        # Use advanced prompting if available
        if self.use_advanced_prompting and self.advanced_prompting:
            query = f"""
            Investment Comparison Analysis:

            Option A: {option_a}
            Option B: {option_b}

            User Profile: {json.dumps(user_profile) if user_profile else "Not provided"}

            Please provide a comprehensive comparison including:
            1. Risk-return analysis for each option
            2. Suitability for the user's profile
            3. ESG and ethical considerations
            4. Market conditions and timing
            5. Clear recommendation with rationale

            Consider the user's risk tolerance, investment horizon, and ethical preferences.
            """

            advanced_result = self.advanced_prompting.analyze_with_technique(
                query, prompting_technique
            )
            response = advanced_result.raw_response
        else:
            # Fallback to standard prompting
            prompt = f"""
            Please provide a comprehensive comparison between these two investment options:

            Option A: {option_a}
            Option B: {option_b}

            User Profile: {json.dumps(user_profile) if user_profile else "Not provided"}

            Please structure your response as follows:
            1. RECOMMENDATION: Clear recommendation with rationale
            2. CONFIDENCE: Confidence level (0-100%)
            3. REASONING: Step-by-step analysis reasoning
            4. ETHICAL_CONSIDERATIONS: ESG and ethical factors
            5. RISK_ASSESSMENT: Risk analysis for each option
            6. TRANSPARENCY: Assumptions and limitations

            Focus on explainability and transparency in your analysis.
            """

            response = self.typhoon_client.query(prompt)
        
        # Parse the structured response (simplified parsing for demo)
        result = self._parse_analysis_response(response, option_a, option_b)
        
        # Store in history
        self.analysis_history.append(result)
        
        return result

    def check_legal_compliance(self, investment_scenario: str, user_profile: Dict[str, Any] = None) -> Dict[str, Any]:
        """Check legal compliance for investment scenario"""

        if not self.enable_legal_compliance or not self.legal_fusion:
            return {
                "compliance_status": "not_checked",
                "compliance_score": 0.5,
                "warnings": ["Legal compliance checking is disabled"],
                "recommendations": []
            }

        # Create compliance check prompt
        compliance_prompt = f"""
        Legal Compliance Assessment for Financial Investment:

        Investment Scenario: {investment_scenario}
        User Profile: {json.dumps(user_profile) if user_profile else "Not provided"}

        Please assess:
        1. Regulatory compliance requirements
        2. Legal restrictions or limitations
        3. Disclosure obligations
        4. Risk management requirements
        5. Fiduciary duty considerations

        Provide a compliance score (0-10) and specific recommendations.
        """

        try:
            compliance_response = self.typhoon_client.query(compliance_prompt)

            # Parse compliance response
            compliance_data = self._parse_compliance_response(compliance_response)

            return compliance_data

        except Exception as e:
            logger.error(f"Legal compliance check failed: {str(e)}")
            return {
                "compliance_status": "error",
                "compliance_score": 0.0,
                "warnings": [f"Compliance check failed: {str(e)}"],
                "recommendations": ["Manual legal review required"]
            }

    def _parse_compliance_response(self, response: str) -> Dict[str, Any]:
        """Parse legal compliance response"""

        # Extract compliance score
        import re
        score_match = re.search(r'(\d+(?:\.\d+)?)\s*[/]?\s*10', response)
        compliance_score = float(score_match.group(1)) / 10 if score_match else 0.5

        # Extract warnings and recommendations
        warnings = []
        recommendations = []

        lines = response.split('\n')
        current_section = None

        for line in lines:
            line = line.strip()
            if 'warning' in line.lower() or 'risk' in line.lower():
                warnings.append(line)
            elif 'recommend' in line.lower() or 'should' in line.lower():
                recommendations.append(line)

        # Determine compliance status
        if compliance_score >= 0.8:
            status = "compliant"
        elif compliance_score >= 0.6:
            status = "partially_compliant"
        else:
            status = "non_compliant"

        return {
            "compliance_status": status,
            "compliance_score": compliance_score,
            "warnings": warnings if warnings else ["No specific warnings identified"],
            "recommendations": recommendations if recommendations else ["Follow standard compliance procedures"],
            "full_response": response
        }

    def _parse_analysis_response(self, response: str, option_a: str, option_b: str) -> FinancialAnalysisResult:
        """Parse the LLM response into structured format"""
        
        # Simplified parsing - in production, use more robust NLP parsing
        lines = response.split('\n')
        
        recommendation = "See detailed analysis below"
        confidence_score = 0.75  # Default
        reasoning = []
        ethical_considerations = []
        risk_assessment = {"option_a": option_a, "option_b": option_b, "analysis": "See response"}
        transparency_report = {"assumptions": [], "limitations": [], "data_sources": []}
        legal_compliance = {"status": "not_assessed", "score": 0.5}
        regulatory_warnings = []
        compliance_score = 0.5
        
        current_section = None
        for line in lines:
            line = line.strip()
            if "RECOMMENDATION:" in line.upper():
                current_section = "recommendation"
                recommendation = line.split(":", 1)[1].strip() if ":" in line else line
            elif "CONFIDENCE:" in line.upper():
                current_section = "confidence"
                # Extract percentage if present
                import re
                confidence_match = re.search(r'(\d+)%?', line)
                if confidence_match:
                    confidence_score = float(confidence_match.group(1)) / 100
            elif "REASONING:" in line.upper():
                current_section = "reasoning"
            elif "ETHICAL" in line.upper():
                current_section = "ethical"
            elif "RISK" in line.upper():
                current_section = "risk"
            elif "TRANSPARENCY:" in line.upper():
                current_section = "transparency"
            elif line and current_section:
                if current_section == "reasoning":
                    reasoning.append(line)
                elif current_section == "ethical":
                    ethical_considerations.append(line)
        
        return FinancialAnalysisResult(
            recommendation=recommendation,
            confidence_score=confidence_score,
            reasoning=reasoning,
            ethical_considerations=ethical_considerations,
            risk_assessment=risk_assessment,
            transparency_report=transparency_report,
            legal_compliance=legal_compliance,
            regulatory_warnings=regulatory_warnings,
            compliance_score=compliance_score,
            timestamp=datetime.now()
        )
    
    def get_analysis_history(self) -> List[FinancialAnalysisResult]:
        """Get history of all analyses"""
        return self.analysis_history
    
    def generate_explanation_report(self, analysis: FinancialAnalysisResult) -> str:
        """Generate a detailed explanation report for transparency"""
        
        report = f"""
        FINANCIAL ANALYSIS EXPLANATION REPORT
        =====================================
        
        Timestamp: {analysis.timestamp}
        Confidence Score: {analysis.confidence_score:.2%}
        
        RECOMMENDATION:
        {analysis.recommendation}
        
        REASONING PROCESS:
        """
        
        for i, reason in enumerate(analysis.reasoning, 1):
            report += f"\n{i}. {reason}"
        
        report += f"""
        
        ETHICAL CONSIDERATIONS:
        """
        
        for i, consideration in enumerate(analysis.ethical_considerations, 1):
            report += f"\n{i}. {consideration}"
        
        report += f"""
        
        RISK ASSESSMENT:
        {json.dumps(analysis.risk_assessment, indent=2)}
        
        TRANSPARENCY REPORT:
        {json.dumps(analysis.transparency_report, indent=2)}
        
        This analysis was generated using explainable AI principles to ensure
        transparency and accountability in financial decision-making.
        """
        
        return report

def main():
    """Demo function for the Financial Analysis Agent"""
    
    # Initialize the agent
    api_key = "sk-RD2ySZFmAa5nP2WrwQfX4Q5ArZViFEkapK4UzS2jkLq8fErt"
    agent = FinancialAnalysisAgent(api_key)
    
    # Example analysis
    print("🤖 Financial Analysis Agent - Hackathon Demo")
    print("=" * 50)
    
    # Test investment comparison
    option_a = "High-growth AI startup with 50% potential return but high volatility"
    option_b = "Low-risk ESG mutual fund with 8% expected annual return"
    
    user_profile = {
        "age": 30,
        "risk_tolerance": "moderate",
        "investment_horizon": "10 years",
        "ethical_preferences": "ESG focused"
    }
    
    print(f"Analyzing: {option_a} vs {option_b}")
    print(f"User Profile: {user_profile}")
    print("\nGenerating analysis...")
    
    result = agent.analyze_investment_comparison(option_a, option_b, user_profile)
    
    print("\n" + "=" * 50)
    print("ANALYSIS RESULT:")
    print("=" * 50)
    print(f"Recommendation: {result.recommendation}")
    print(f"Confidence: {result.confidence_score:.2%}")
    
    print("\nDetailed Explanation Report:")
    print(agent.generate_explanation_report(result))

if __name__ == "__main__":
    main()
