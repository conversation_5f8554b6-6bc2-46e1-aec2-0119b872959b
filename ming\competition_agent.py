"""
Competition Financial Analysis Agent
Specialized for handling the hackathon competition dataset with multiple choice questions and stock predictions
"""

import pandas as pd
import json
import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from typhoon_client import TyphoonAPIClient
from advanced_prompting_engine import AdvancedPromptingEngine, PromptingTechnique, FinancialSector

logger = logging.getLogger(__name__)

@dataclass
class CompetitionAnswer:
    """Structure for competition answers"""
    question_id: str
    question_type: str
    raw_response: str
    extracted_answer: str
    confidence: float
    reasoning: List[str]

class CompetitionAgent:
    """Specialized agent for the financial analysis competition"""
    
    def __init__(self, api_key: str):
        self.typhoon_client = TyphoonAPIClient(api_key)
        self.advanced_prompting = AdvancedPromptingEngine(api_key)
        self.results = []
        
    def detect_question_type(self, query: str) -> str:
        """Detect the type of question"""
        query_lower = query.lower()
        
        # Check for stock prediction questions
        if any(keyword in query_lower for keyword in ['rise', 'fall', 'ขึ้น', 'ลง', 'closing price', 'ราคาปิด']):
            return "stock_prediction"
        
        # Check for multiple choice questions
        if any(keyword in query_lower for keyword in ['answer choices:', 'ตัวเลือก:', 'options:']):
            return "multiple_choice"
        
        # Check for Thai compliance questions
        if any(keyword in query_lower for keyword in ['abc asset management', 'sec thailand', 'bot']):
            return "compliance"
        
        return "general"
    
    def extract_stock_data(self, query: str) -> Dict[str, Any]:
        """Extract stock symbol and data from query"""
        # Extract stock symbol
        symbol_match = re.search(r'\$([a-zA-Z]+)', query)
        symbol = symbol_match.group(1) if symbol_match else "UNKNOWN"
        
        # Extract date
        date_match = re.search(r'(\d{4}-\d{2}-\d{2})', query)
        target_date = date_match.group(1) if date_match else "UNKNOWN"
        
        # Extract price data (simplified)
        price_data = []
        lines = query.split('\n')
        for line in lines:
            if re.match(r'\d{4}-\d{2}-\d{2}', line.strip()):
                price_data.append(line.strip())
        
        # Extract tweets/social sentiment
        tweets = []
        for line in lines:
            if '$' in line and any(word in line.lower() for word in ['rt', '@', '#']):
                tweets.append(line.strip())
        
        return {
            'symbol': symbol,
            'target_date': target_date,
            'price_data': price_data[:10],  # Last 10 days
            'tweets': tweets[:5],  # Recent tweets
            'has_data': len(price_data) > 0
        }
    
    def analyze_stock_prediction(self, query: str) -> CompetitionAnswer:
        """Analyze stock prediction questions using advanced prompting"""
        
        stock_data = self.extract_stock_data(query)
        
        # Create enhanced prompt for stock prediction
        enhanced_prompt = f"""
        Stock Price Prediction Analysis for {stock_data['symbol']}
        Target Date: {stock_data['target_date']}
        
        Historical Price Data (last 10 days):
        {chr(10).join(stock_data['price_data'])}
        
        Recent Social Media Sentiment:
        {chr(10).join(stock_data['tweets'][:3])}
        
        Task: Predict whether the closing price will Rise or Fall on the target date.
        
        Analysis Framework:
        1. Technical Analysis: Examine price trends, volatility patterns
        2. Sentiment Analysis: Evaluate social media sentiment and news
        3. Market Context: Consider broader market conditions
        4. Risk Factors: Identify key risks that could affect price
        
        Based on your analysis, provide:
        - Clear prediction: Rise or Fall
        - Confidence level (0-100%)
        - Key reasoning factors
        
        Answer with either "Rise" or "Fall" only.
        """
        
        # Use Financial CoT for best results
        result = self.advanced_prompting.analyze_with_technique(
            enhanced_prompt, PromptingTechnique.FINANCIAL_COT
        )
        
        # Extract Rise/Fall answer
        answer = self._extract_rise_fall_answer(result.raw_response)
        
        return CompetitionAnswer(
            question_id="",
            question_type="stock_prediction",
            raw_response=result.raw_response,
            extracted_answer=answer,
            confidence=result.confidence,
            reasoning=result.reasoning
        )
    
    def analyze_multiple_choice(self, query: str) -> CompetitionAnswer:
        """Analyze multiple choice questions"""
        
        # Enhanced prompt for multiple choice
        enhanced_prompt = f"""
        Financial Knowledge Question:
        
        {query}
        
        Instructions:
        1. Read the question carefully
        2. Analyze each answer choice
        3. Apply relevant financial theory and principles
        4. Select the most appropriate answer
        5. Respond with ONLY the letter (A, B, C, or D)
        
        Think step-by-step through the financial concepts involved.
        """
        
        # Use Structured CoT for multiple choice
        result = self.advanced_prompting.analyze_with_technique(
            enhanced_prompt, PromptingTechnique.STRUCTURED_COT
        )
        
        # Extract A/B/C/D answer
        answer = self._extract_multiple_choice_answer(result.raw_response)
        
        return CompetitionAnswer(
            question_id="",
            question_type="multiple_choice",
            raw_response=result.raw_response,
            extracted_answer=answer,
            confidence=result.confidence,
            reasoning=result.reasoning
        )
    
    def analyze_compliance_question(self, query: str) -> CompetitionAnswer:
        """Analyze compliance and regulatory questions"""
        
        enhanced_prompt = f"""
        Financial Compliance and Regulatory Analysis:
        
        {query}
        
        Context: This involves Thai financial regulations, SEC Thailand, Bank of Thailand (BOT), 
        and asset management compliance requirements.
        
        Analysis Framework:
        1. Identify the regulatory issue
        2. Consider relevant Thai financial laws and BOT guidelines
        3. Evaluate ethical implications
        4. Assess compliance requirements
        5. Determine the most appropriate action
        
        Provide the correct answer choice (A, B, C, or D) based on:
        - Thai regulatory requirements
        - Best practices in compliance
        - Ethical standards in finance
        - Risk management principles
        """
        
        # Use Financial CoT for compliance questions
        result = self.advanced_prompting.analyze_with_technique(
            enhanced_prompt, PromptingTechnique.FINANCIAL_COT
        )
        
        # Extract answer
        answer = self._extract_multiple_choice_answer(result.raw_response)
        
        return CompetitionAnswer(
            question_id="",
            question_type="compliance",
            raw_response=result.raw_response,
            extracted_answer=answer,
            confidence=result.confidence,
            reasoning=result.reasoning
        )
    
    def _extract_rise_fall_answer(self, response: str) -> str:
        """Extract Rise/Fall answer from response"""
        response_lower = response.lower()
        
        # Look for explicit Rise/Fall
        if 'rise' in response_lower and 'fall' not in response_lower:
            return "Rise"
        elif 'fall' in response_lower and 'rise' not in response_lower:
            return "Fall"
        
        # Look for Thai equivalents
        if 'ขึ้น' in response:
            return "Rise"
        elif 'ลง' in response:
            return "Fall"
        
        # Look for positive/negative indicators
        positive_indicators = ['increase', 'up', 'bullish', 'positive', 'gain']
        negative_indicators = ['decrease', 'down', 'bearish', 'negative', 'loss']
        
        positive_count = sum(1 for word in positive_indicators if word in response_lower)
        negative_count = sum(1 for word in negative_indicators if word in response_lower)
        
        if positive_count > negative_count:
            return "Rise"
        elif negative_count > positive_count:
            return "Fall"
        
        # Default to Rise if unclear
        return "Rise"
    
    def _extract_multiple_choice_answer(self, response: str) -> str:
        """Extract A/B/C/D answer from response"""
        
        # Look for explicit answer patterns
        patterns = [
            r'\b([ABCD])\b',
            r'answer[:\s]*([ABCD])',
            r'choice[:\s]*([ABCD])',
            r'option[:\s]*([ABCD])',
            r'คำตอบ[:\s]*([ABCD])'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, response, re.IGNORECASE)
            if matches:
                return matches[-1].upper()  # Return the last match
        
        # Look in output tags
        if '<output>' in response and '</output>' in response:
            output_start = response.find('<output>') + len('<output>')
            output_end = response.find('</output>')
            output_content = response[output_start:output_end]
            
            for pattern in patterns:
                matches = re.findall(pattern, output_content, re.IGNORECASE)
                if matches:
                    return matches[-1].upper()
        
        # Default to A if no clear answer found
        return "A"
    
    def process_competition_dataset(self, test_file: str = "test.csv") -> pd.DataFrame:
        """Process the entire competition dataset"""
        
        # Load test data
        df = pd.read_csv(test_file)
        results = []
        
        print(f"Processing {len(df)} questions...")
        
        for idx, row in df.iterrows():
            question_id = row['id']
            query = row['query']
            
            print(f"Processing question {idx + 1}/{len(df)}: {question_id}")
            
            try:
                # Detect question type
                question_type = self.detect_question_type(query)
                
                # Analyze based on type
                if question_type == "stock_prediction":
                    result = self.analyze_stock_prediction(query)
                elif question_type == "compliance":
                    result = self.analyze_compliance_question(query)
                else:
                    result = self.analyze_multiple_choice(query)
                
                result.question_id = question_id
                
                results.append({
                    'id': question_id,
                    'answer': result.extracted_answer,
                    'confidence': result.confidence,
                    'question_type': question_type
                })
                
                print(f"  Answer: {result.extracted_answer} (Confidence: {result.confidence:.1%})")
                
            except Exception as e:
                print(f"  Error processing question: {str(e)}")
                results.append({
                    'id': question_id,
                    'answer': 'A',  # Default fallback
                    'confidence': 0.5,
                    'question_type': 'error'
                })
        
        return pd.DataFrame(results)
    
    def generate_submission(self, test_file: str = "test.csv", output_file: str = "my_submission.csv"):
        """Generate submission file for the competition"""
        
        print("🏆 Generating Competition Submission")
        print("=" * 50)
        
        # Process all questions
        results_df = self.process_competition_dataset(test_file)
        
        # Create submission format
        submission_df = results_df[['id', 'answer']].copy()
        
        # Save submission
        submission_df.to_csv(output_file, index=False)
        
        print(f"\n✅ Submission saved to: {output_file}")
        print(f"📊 Total questions processed: {len(submission_df)}")
        
        # Show statistics
        question_types = results_df['question_type'].value_counts()
        print(f"\n📈 Question Type Distribution:")
        for qtype, count in question_types.items():
            print(f"  {qtype}: {count}")
        
        avg_confidence = results_df['confidence'].mean()
        print(f"\n🎯 Average Confidence: {avg_confidence:.1%}")
        
        return submission_df

def main():
    """Demo the competition agent"""
    
    print("🏆 Financial Analysis Competition Agent")
    print("=" * 50)
    
    # Initialize agent
    api_key = "sk-RD2ySZFmAa5nP2WrwQfX4Q5ArZViFEkapK4UzS2jkLq8fErt"
    agent = CompetitionAgent(api_key)
    
    # Generate submission
    submission = agent.generate_submission()
    
    print("\n🎉 Competition submission ready!")
    print("File: my_submission.csv")

if __name__ == "__main__":
    main()
