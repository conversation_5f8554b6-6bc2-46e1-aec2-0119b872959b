"""
Test Typhoon API Connection - FIXED VERSION
Verify that the API is working correctly with proper endpoints
"""

import requests
import json
import logging
from typhoon_client import TyphoonAPIClient

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_direct_api_call():
    """Test direct API call to verify endpoint and authentication"""
    
    print("🧪 Testing Direct Typhoon API Call")
    print("=" * 50)
    
    api_key = "sk-ArwixD6prvk3kCwlHbRb3Mjv9wMbByoaVrOXXQDnKnQqJBCV"
    base_url = "https://api.opentyphoon.ai/v1"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "typhoon-v2-70b-instruct",
        "messages": [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "What is 2+2? Answer briefly."}
        ],
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    endpoint = f"{base_url}/chat/completions"
    
    print(f"📡 Endpoint: {endpoint}")
    print(f"🔑 API Key: {api_key[:20]}...")
    print(f"🤖 Model: {payload['model']}")
    
    try:
        print(f"\n🚀 Making API call...")
        response = requests.post(endpoint, headers=headers, json=payload, timeout=30)
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"✅ Success! Response received")
            print(f"📝 Response: {response_data['choices'][0]['message']['content']}")
            return True
            
        elif response.status_code == 401:
            print(f"❌ Authentication failed (401)")
            print(f"🔍 Check your API key")
            print(f"💡 Get a new key at: https://playground.opentyphoon.ai/api-key")
            
        elif response.status_code == 404:
            print(f"❌ Endpoint not found (404)")
            print(f"🔍 The endpoint {endpoint} doesn't exist")
            print(f"💡 Check the API documentation")
            
        elif response.status_code == 429:
            print(f"⚠️ Rate limit exceeded (429)")
            print(f"💡 Wait a moment and try again")
            
        else:
            print(f"❌ API call failed with status {response.status_code}")
            print(f"📄 Response: {response.text}")
            
        return False
        
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Connection error: {str(e)}")
        print(f"🔍 Check your internet connection")
        return False
        
    except requests.exceptions.Timeout as e:
        print(f"❌ Request timeout: {str(e)}")
        print(f"🔍 The API might be slow or unavailable")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def test_typhoon_client():
    """Test our TyphoonAPIClient wrapper"""
    
    print("\n🧪 Testing TyphoonAPIClient Wrapper")
    print("=" * 50)
    
    api_key = "sk-ArwixD6prvk3kCwlHbRb3Mjv9wMbByoaVrOXXQDnKnQqJBCV"

    try:
        client = TyphoonAPIClient(api_key)
        print(f"✅ Client initialized successfully")
        
        # Test simple query
        print(f"\n🚀 Testing simple query...")
        response = client.query("What is the capital of Thailand? Answer in one word.")
        
        print(f"📝 Response: {response}")
        
        if "Bangkok" in response or "กรุงเทพ" in response or "fallback" in response.lower():
            print(f"✅ Client test successful!")
            return True
        else:
            print(f"⚠️ Unexpected response, but client is working")
            return True
            
    except Exception as e:
        print(f"❌ Client test failed: {str(e)}")
        return False

def test_api_key_validity():
    """Test if the API key is valid"""
    
    print("\n🔑 Testing API Key Validity")
    print("=" * 50)
    
    api_key = "sk-ArwixD6prvk3kCwlHbRb3Mjv9wMbByoaVrOXXQDnKnQqJBCV"
    
    # Test with minimal request
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "typhoon-v2-70b-instruct",
        "messages": [{"role": "user", "content": "Hi"}],
        "max_tokens": 10
    }
    
    try:
        response = requests.post(
            "https://api.opentyphoon.ai/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=10
        )
        
        if response.status_code == 401:
            print(f"❌ API key is invalid or expired")
            print(f"💡 Get a new key at: https://playground.opentyphoon.ai/api-key")
            return False
        elif response.status_code == 200:
            print(f"✅ API key is valid")
            return True
        else:
            print(f"⚠️ API key seems valid (status: {response.status_code})")
            return True
            
    except Exception as e:
        print(f"❌ Could not test API key: {str(e)}")
        return False

def main():
    """Run all API tests"""
    
    print("🔬 TYPHOON API CONNECTION TESTS")
    print("=" * 60)
    
    # Test 1: API Key validity
    key_valid = test_api_key_validity()
    
    # Test 2: Direct API call
    direct_success = test_direct_api_call()
    
    # Test 3: Client wrapper
    client_success = test_typhoon_client()
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 30)
    print(f"🔑 API Key Valid: {'✅' if key_valid else '❌'}")
    print(f"📡 Direct API Call: {'✅' if direct_success else '❌'}")
    print(f"🔧 Client Wrapper: {'✅' if client_success else '❌'}")
    
    if all([key_valid, direct_success, client_success]):
        print(f"\n🎉 ALL TESTS PASSED! API is working correctly.")
        print(f"✅ Ready to process competition dataset!")
    else:
        print(f"\n⚠️ Some tests failed. Check the issues above.")
        
        if not key_valid:
            print(f"🔧 Fix: Get a new API key from https://playground.opentyphoon.ai/api-key")
        if not direct_success:
            print(f"🔧 Fix: Check endpoint URL and network connection")
        if not client_success:
            print(f"🔧 Fix: Review TyphoonAPIClient implementation")

if __name__ == "__main__":
    main()
