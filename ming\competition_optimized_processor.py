"""
Competition-Optimized Financial Analysis Processor
Implements advanced prompting techniques from hackathon guidance to maximize accuracy
"""

import json
import logging
import re
import statistics
import collections
from typing import Dict, List, Any, Optional, Tuple
from tqdm import tqdm
import pandas as pd
from datetime import datetime

from typhoon_client import TyphoonAPIClient
from dataset_integration import FinancialDatasetManager
from legal_financial_fusion import LegalFinancialFusion

logger = logging.getLogger(__name__)

class CompetitionOptimizedProcessor:
    """Optimized processor using hackathon guidance techniques"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.typhoon_client = TyphoonAPIClient(api_key)
        self.dataset_manager = FinancialDatasetManager()
        self.legal_fusion = LegalFinancialFusion()
        
        # Competition parameters
        self.n_samples = 5  # Self-consistency sampling
        self.temperature = 0.7
        self.max_tokens = 16384
        
        # Performance tracking
        self.accuracy_history = []
        self.processing_times = []
        
    def get_financial_cot_prompt(self) -> str:
        """Get the Financial Chain-of-Thought system prompt from hackathon guidance"""
        return """You are a financial analyst taking a test to evaluate your knowledge of finance of different topics in finance. You think step by step approach with reflection to answer queries. 

Follow these steps:
1. Think through the problem step by step reflect and verify while reasoning within the <thinking> tags.
2. Please and put the answer your final, concise answer within the <output> tags.

The <thinking> sections are for your internal reasoning process only. 
Do not include any part of the final answer in these sections.
The actual response to the query must be entirely contained within the <output> tags.

Hint: ***Financial Reporting:**
```mermaid
graph TD
A[Articulating Purpose and Context] --> B[Collecting Input Data]
    B --> C[Processing Data]
    C --> D[Analyzing and Interpreting Processed Data]
    D --> E[Developing and Communicating Conclusions]
    E --> F[Doing Follow-Up]

    A --> |Defines goals, tools, and audience| B
    B --> |Gather data on economy and industry| C
    C --> |Use tools like ratios and charts| D
    D --> |Interpret data for conclusions| E
    F --> |Periodic review and iteration| A
```
***Fixed Income:***
```mermaid
graph TD
    A[Purpose and Scope] --> B[Analyze Macro Conditions]
    B --> C[Assess Bond Features]
    C --> D[Risk and Yield Analysis]
    D --> E[Develop Recommendations]
    E --> F[Review Performance]

    A --> |Set objectives| B
    B --> |Review interest rates and inflation| C
    C --> |Focus on duration, spread| D
    D --> |Assess scenarios| E
``` 
***Equity Investing:*** 
```mermaid
graph TD
    A[Objective Setting] --> B[Market and Sector Insights]
    B --> C[Industry Competitive Analysis]
    C --> D[Company Review]
    D --> E[Valuation and Risks]
    E --> F[Investment Decision]

    B --> |Look at growth patterns| C
    C --> |Evaluate competitors' positions| D
    D --> |Check financial health| E
    E --> |Combine insights into strategy| F
```
***Derivatives:*** 
```mermaid
graph TD
    A[Define Objective and Context] --> B[Identify Derivative Instrument]
    B --> C[Understand Contract Specifications]
    C --> D[Gather Market Data]
    D --> E[Apply Valuation Models]
    E --> F[Assess Risks: Market, Counterparty, etc.]
    F --> G[Construct Payoff Diagrams or Strategies]
    G --> H[Interpret Results and Make Recommendations]
    H --> I[Review, Monitor, and Adjust Strategies]

    A --> |Hedging, speculation, arbitrage| B
    C --> |Features like notional amount, expiration| D
    D --> |Market prices, volatility, risk-free rates| E
    F --> |Sensitivity to Greeks: Delta, Gamma, Vega, etc.| G
    H --> |Adjust based on changing market conditions| I
```
***Economics:*** 
```mermaid
graph TD;
    A[Step 1: Question Breakdown] -->|Extract key terms| A1{Identify Topic}
    A1 -->|Micro: Supply & Demand, Market Structures| A2
    A1 -->|Macro: GDP, Growth, Policy, Trade| A3
    A1 -->|Currency & Regulation| A4

    A2 --> B1[Identify model: Elasticity, Cost Curves, Shutdown Points]
    A3 --> B2[Map to AD-AS, Business Cycles, Growth Theories]
    A4 --> B3[Assess Exchange Rates, Trade, Capital Flows, Regulation]

    B1 -->|Check for formula or concept?| C{Numerical or Conceptual}
    B2 --> C
    B3 --> C

    C -->|Numerical| D1[Extract data, apply formulas, check assumptions]
    C -->|Conceptual| D2[Analyze cause-effect, policy impact]

    D1 --> E[Step 4: Solution Development]
    D2 --> E
    E -->|Construct structured response| E1(Core insight + economic rationale)
    E -->|Consider alternative scenarios| E2(Assess different possibilities)

    E1 --> F[Step 5: Answer Validation]
    E2 --> F
    F -->|Check logic, principles, and assumptions| F1(Verify consistency)
    F1 -->|Ensure completeness & clarity| F2(Confirm answer structure)
```
***Quantitative Methods:*** 
```mermaid
graph TD
    A["Articulating Purpose and Context"] --> B["Collecting Input Data"]
    B --> C["Processing and Cleaning Data"]
    C --> D["Selecting Quantitative Models and Tools"]
    D --> E["Estimating Parameters and Testing Hypotheses"]
    E --> F["Interpreting Results and Communicating Findings"]
    F --> G["Monitoring and Model Reassessment"]
```
***Portfolio Management:*** 
```mermaid
graph TD
    A["Define Investment Objectives"] --> B["Establish Investment Constraints"]
    B --> C["Develop Strategic Asset Allocation"]
    C --> D["Incorporate Tactical Adjustments"]
    D --> E["Select and Optimize Securities"]
    E --> F["Execute Implementation and Trading"]
    F --> G["Measure Performance and Attribution"]
    G --> H["Monitor Risk and Compliance"]
    H --> I["Rebalance and Adjust Portfolio"]
```
***Alternative Investments:*** 
```mermaid
graph TD
    A["Define Investment Objectives and Mandate"] --> B["Identify Alternative Asset Classes"]
    B --> C["Conduct Manager and Strategy Due Diligence"]
    C --> D["Perform Valuation and Pricing Analysis"]
    D --> E["Assess Risk and Liquidity"]
    E --> F["Allocate Alternatives in Portfolio"]
    F --> G["Monitor Performance and Rebalance"]
```
***Corporate Issuer Analysis:*** 
```mermaid
graph TD
    A["Corporate Issuer Overview"] --> B["Industry Classification"]
    B --> C["Sector Trends and Competitive Landscape"]
    A --> D["Financial Statement Analysis"]
    D --> E["Profitability, Liquidity, Leverage"]
    A --> F["Credit Risk Assessment"]
    F --> G["Rating Agencies and Default Probabilities"]
    A --> H["Capital Structure and Issuance History"]
    H --> I["Bond Issuances and Debt Maturities"]
    A --> J["Corporate Governance and Management"]
    J --> K["Board Quality and Managerial Competence"]
    A --> L["Valuation and Investment Analysis"]
    L --> M["DCF, Relative Valuation, Multiples"]
```
### Response Format:
<thinking>
[Think step by step and respond with your thinking and the correct answer (A , B , or C ), considering the specific sector.]
</thinking>

<output>
"sector": [The sector being addressed],
"question": [The financial question],
"answer": [Reflect and verify the final answer (A , B , or C )]
</output>"""

    def extract_choice(self, response_text: str) -> Optional[str]:
        """Extract choice from model response using hackathon guidance method"""
        cleaned_text = re.sub(r"outputs:.*?\|.*?\|", "", response_text)
        cleaned_text = cleaned_text.strip().replace("\n", " ")

        # Look for final answer pattern
        match = re.search(r"Final answer:\s*([A-C])", cleaned_text, re.IGNORECASE)
        if match:
            return match.group(1).upper()

        # Look for answer in output tags
        output_match = re.search(r"<output>.*?\"answer\":\s*\"?([A-C])\"?.*?</output>", cleaned_text, re.DOTALL | re.IGNORECASE)
        if output_match:
            return output_match.group(1).upper()

        # Fallback to any A, B, C pattern
        find_choice = re.findall(r"\b([A-C])\b", cleaned_text)
        if find_choice:
            return find_choice[-1].upper()

        return None

    def call_once(self, prompt_sys: str, prompt_usr: str) -> str:
        """Single stochastic call to Typhoon API"""
        try:
            response = self.typhoon_client.query_with_system_prompt(
                user_prompt=prompt_usr,
                system_prompt=prompt_sys,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            return response
        except Exception as e:
            logger.error(f"API call failed: {str(e)}")
            return f"Error: {str(e)}"

    def self_consistent_answer(self, prompt_sys: str, prompt_usr: str, k: int = None, verbose: bool = True) -> Tuple[str, List[str]]:
        """Self-consistency sampling for improved accuracy"""
        if k is None:
            k = self.n_samples
            
        sub_predictions = []
        
        for i in tqdm(range(1, k + 1), desc="Self-consistency sampling", unit="sample"):
            out = self.call_once(prompt_sys, prompt_usr)
            pick = self.extract_choice(out)
            if pick:
                sub_predictions.append(pick)
                if verbose:
                    print(f"prediction-{i}: {pick}")

        # Majority vote
        if sub_predictions:
            vote = collections.Counter(sub_predictions).most_common(1)[0][0]
            return vote, sub_predictions

        # Fallback if all parsing failed
        raw = self.call_once(prompt_sys, prompt_usr)
        return self.extract_choice(raw) or "A", [raw]  # Default to A if all fails

    def process_financial_question(self, question: str, choices: List[str] = None, use_self_consistency: bool = True) -> Dict[str, Any]:
        """Process a financial question with optimized techniques"""
        
        start_time = datetime.now()
        
        # Format the question for FinCoT
        if choices:
            formatted_question = f"{question}\nAnswer Choices: {', '.join([f'{chr(65+i)}: {choice}' for i, choice in enumerate(choices)])}"
        else:
            formatted_question = question
        
        # Get system prompt
        system_prompt = self.get_financial_cot_prompt()
        
        # Process with self-consistency if enabled
        if use_self_consistency:
            final_answer, all_samples = self.self_consistent_answer(
                system_prompt, 
                formatted_question, 
                k=self.n_samples,
                verbose=True
            )
            confidence = all_samples.count(final_answer) / len(all_samples) if all_samples else 0.0
        else:
            # Single call
            response = self.call_once(system_prompt, formatted_question)
            final_answer = self.extract_choice(response)
            all_samples = [final_answer] if final_answer else []
            confidence = 1.0 if final_answer else 0.0
        
        processing_time = (datetime.now() - start_time).total_seconds()
        self.processing_times.append(processing_time)
        
        result = {
            "question": question,
            "final_answer": final_answer,
            "confidence": confidence,
            "all_samples": all_samples,
            "processing_time": processing_time,
            "timestamp": datetime.now().isoformat(),
            "technique": "FinCoT + Self-Consistency" if use_self_consistency else "FinCoT"
        }
        
        return result

    def process_test_dataset(self, test_file: str = "test.csv", output_file: str = "competition_submission.csv") -> Dict[str, Any]:
        """Process the test dataset with competition optimization"""
        
        print("🏆 Competition-Optimized Processing Started")
        print("=" * 60)
        
        # Load test data
        try:
            df = pd.read_csv(test_file)
            print(f"Loaded {len(df)} test questions")
        except Exception as e:
            logger.error(f"Failed to load test file: {str(e)}")
            return {"error": f"Failed to load test file: {str(e)}"}
        
        results = []
        correct_answers = 0
        total_questions = len(df)
        
        # Process each question
        for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing questions"):
            question = row.get('question', '')
            correct_answer = row.get('answer', '')
            
            # Process with optimized techniques
            result = self.process_financial_question(
                question=question,
                use_self_consistency=True
            )
            
            # Check accuracy if we have the correct answer
            if correct_answer and result['final_answer']:
                is_correct = result['final_answer'].upper() == correct_answer.upper()
                if is_correct:
                    correct_answers += 1
                result['is_correct'] = is_correct
                result['correct_answer'] = correct_answer
            
            results.append(result)
            
            # Progress update
            if (idx + 1) % 10 == 0:
                current_accuracy = correct_answers / (idx + 1) if correct_answer else 0
                print(f"Processed {idx + 1}/{total_questions}, Current Accuracy: {current_accuracy:.3f}")
        
        # Calculate final metrics
        final_accuracy = correct_answers / total_questions if total_questions > 0 else 0
        avg_processing_time = statistics.mean(self.processing_times) if self.processing_times else 0
        avg_confidence = statistics.mean([r['confidence'] for r in results]) if results else 0
        
        # Save submission file
        submission_df = pd.DataFrame([
            {
                'question_id': i,
                'answer': result['final_answer'] or 'A',  # Default to A if no answer
                'confidence': result['confidence']
            }
            for i, result in enumerate(results)
        ])
        
        submission_df.to_csv(output_file, index=False)
        
        # Performance summary
        performance_summary = {
            "total_questions": total_questions,
            "correct_answers": correct_answers,
            "final_accuracy": final_accuracy,
            "avg_processing_time": avg_processing_time,
            "avg_confidence": avg_confidence,
            "technique_used": "FinCoT + Self-Consistency",
            "n_samples": self.n_samples,
            "submission_file": output_file,
            "timestamp": datetime.now().isoformat()
        }
        
        # Save performance report
        with open("competition_performance_report.json", "w") as f:
            json.dump(performance_summary, f, indent=2)
        
        print("\n" + "=" * 60)
        print("🎯 COMPETITION RESULTS")
        print("=" * 60)
        print(f"Final Accuracy: {final_accuracy:.4f} ({final_accuracy*100:.2f}%)")
        print(f"Improvement over baseline (0.28): {((final_accuracy - 0.28) / 0.28 * 100):+.1f}%")
        print(f"Average Confidence: {avg_confidence:.3f}")
        print(f"Average Processing Time: {avg_processing_time:.2f}s per question")
        print(f"Submission saved to: {output_file}")
        
        return performance_summary

def main():
    """Main competition optimization function"""
    
    # Initialize with updated API key
    api_key = "sk-ArwixD6prvk3kCwlHbRb3Mjv9wMbByoaVrOXXQDnKnQqJBCV"
    processor = CompetitionOptimizedProcessor(api_key)
    
    # Test with a sample question first
    print("🧪 Testing with sample question...")
    sample_result = processor.process_financial_question(
        question="What is the primary risk associated with high-yield bonds? A) Interest rate risk B) Credit risk C) Liquidity risk",
        use_self_consistency=True
    )
    
    print(f"Sample Result: {sample_result['final_answer']} (Confidence: {sample_result['confidence']:.2f})")
    
    # Process full test dataset
    print("\n🚀 Starting full competition processing...")
    results = processor.process_test_dataset()
    
    return results

if __name__ == "__main__":
    main()
