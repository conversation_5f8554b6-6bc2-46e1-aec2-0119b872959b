"""
Final Competition Processor with Complete Performance Tracking
Generates submission.csv with detailed timing and performance metrics
"""

import pandas as pd
import numpy as np
import time
import json
import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import concurrent.futures
import multiprocessing as mp
from datetime import datetime, timedelta

from typhoon_client import Typhoon<PERSON>IClient

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class QuestionResult:
    """Detailed result for each question"""
    question_id: str
    answer: str
    confidence: float
    question_type: str
    processing_time: float
    attempt_number: int
    api_success: bool

@dataclass
class PerformanceMetrics:
    """Complete performance tracking"""
    total_questions: int
    total_time: float
    avg_time_per_question: float
    questions_per_minute: float
    questions_per_second: float
    successful_api_calls: int
    failed_api_calls: int
    fallback_responses: int
    confidence_distribution: Dict[str, float]
    question_type_distribution: Dict[str, int]
    processing_timeline: List[Tuple[str, float, str]]  # (question_id, timestamp, status)

class FinalCompetitionProcessor:
    """Final processor with complete performance tracking"""
    
    def __init__(self, api_key: str, max_workers: int = None):
        self.api_key = api_key
        self.max_workers = max_workers or min(32, (mp.cpu_count() or 1) + 4)
        self.typhoon_client = TyphoonAPIClient(api_key)
        
        # Performance tracking
        self.start_time = None
        self.results = []
        self.performance_log = []
        self.api_call_count = 0
        self.successful_calls = 0
        self.failed_calls = 0
        
        # Pre-compiled patterns for speed
        self.stock_symbol_pattern = re.compile(r'\$([a-zA-Z]+)')
        self.date_pattern = re.compile(r'(\d{4}-\d{2}-\d{2})')
        self.choice_patterns = [
            re.compile(r'\b([ABCD])\b'),
            re.compile(r'answer[:\s]*([ABCD])', re.IGNORECASE),
            re.compile(r'choice[:\s]*([ABCD])', re.IGNORECASE)
        ]
        
        print(f"🚀 Final Competition Processor initialized")
        print(f"💻 Max workers: {self.max_workers}")
        print(f"🔧 CPU cores available: {mp.cpu_count()}")
    
    def classify_question_fast(self, query: str) -> str:
        """Fast question classification"""
        query_lower = query.lower()
        
        if any(kw in query_lower for kw in ['rise', 'fall', 'ขึ้น', 'ลง', 'closing price', 'ราคาปิด']):
            return "stock_prediction"
        elif 'abc asset management' in query_lower or 'sec thailand' in query_lower:
            return "compliance"
        else:
            return "multiple_choice"
    
    def create_optimized_prompt(self, query: str, question_type: str) -> str:
        """Create optimized prompts"""
        if question_type == "stock_prediction":
            return f"""Stock Price Analysis:
{query[:800]}

Based on the data and sentiment, predict: Rise or Fall
Answer with only: Rise or Fall"""
        
        elif question_type == "compliance":
            return f"""Thai Financial Compliance:
{query[:1000]}

Apply BOT/SEC Thailand regulations.
Answer with only: A, B, C, or D"""
        
        else:
            return f"""Financial Knowledge:
{query[:800]}

Apply financial principles.
Answer with only: A, B, C, or D"""
    
    def extract_answer_fast(self, response: str, question_type: str) -> str:
        """Fast answer extraction"""
        if question_type == "stock_prediction":
            response_lower = response.lower()
            if 'rise' in response_lower or 'ขึ้น' in response:
                return "Rise"
            elif 'fall' in response_lower or 'ลง' in response:
                return "Fall"
            else:
                return "Rise"  # Default optimistic
        else:
            for pattern in self.choice_patterns:
                matches = pattern.findall(response)
                if matches:
                    return matches[-1].upper()
            return "A"  # Default
    
    def process_single_question(self, question_data: Tuple[str, str, int]) -> QuestionResult:
        """Process single question with detailed tracking"""
        question_id, query, question_index = question_data
        start_time = time.time()
        
        try:
            # Log start
            self.performance_log.append((question_id, start_time, "START"))
            
            # Classify question
            question_type = self.classify_question_fast(query)
            
            # Create prompt
            prompt = self.create_optimized_prompt(query, question_type)
            
            # API call
            api_start = time.time()
            self.api_call_count += 1
            
            try:
                response = self.typhoon_client.query(prompt, temperature=0.3)
                api_success = True
                self.successful_calls += 1
            except Exception as e:
                # Fallback response
                response = self._generate_smart_fallback(query, question_type)
                api_success = False
                self.failed_calls += 1
            
            api_time = time.time() - api_start
            
            # Extract answer
            answer = self.extract_answer_fast(response, question_type)
            
            # Calculate confidence
            confidence = 0.85 if api_success and len(response) > 100 else 0.75
            
            processing_time = time.time() - start_time
            
            # Log completion
            self.performance_log.append((question_id, time.time(), "COMPLETE"))
            
            return QuestionResult(
                question_id=question_id,
                answer=answer,
                confidence=confidence,
                question_type=question_type,
                processing_time=processing_time,
                attempt_number=1,
                api_success=api_success
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Error processing {question_id}: {str(e)}")
            
            return QuestionResult(
                question_id=question_id,
                answer="A",
                confidence=0.5,
                question_type="error",
                processing_time=processing_time,
                attempt_number=1,
                api_success=False
            )
    
    def _generate_smart_fallback(self, query: str, question_type: str) -> str:
        """Generate intelligent fallback responses"""
        if question_type == "stock_prediction":
            # Simple sentiment analysis
            positive_words = ['growth', 'profit', 'gain', 'bullish', 'strong']
            negative_words = ['loss', 'decline', 'bearish', 'weak', 'fall']
            
            query_lower = query.lower()
            pos_score = sum(1 for word in positive_words if word in query_lower)
            neg_score = sum(1 for word in negative_words if word in query_lower)
            
            return "Rise" if pos_score >= neg_score else "Fall"
        else:
            # For multiple choice, use pattern-based fallback
            if 'risk' in query.lower():
                return "D"  # Often the most comprehensive answer
            elif 'regulation' in query.lower():
                return "C"  # Often compliance-related
            else:
                return "B"  # Statistical best choice for many exams
    
    def process_with_detailed_tracking(self, test_file: str = "test.csv") -> Tuple[pd.DataFrame, PerformanceMetrics]:
        """Process with complete performance tracking"""
        
        print("🚀 FINAL COMPETITION PROCESSING WITH DETAILED TRACKING")
        print("=" * 70)
        
        self.start_time = time.time()
        start_datetime = datetime.now()
        
        # Load dataset
        df = pd.read_csv(test_file)
        total_questions = len(df)
        
        print(f"📊 Total questions: {total_questions}")
        print(f"🕐 Start time: {start_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"💻 Using {self.max_workers} parallel workers")
        
        # Prepare question data with indices
        question_data = [(row['id'], row['query'], idx) for idx, (_, row) in enumerate(df.iterrows())]
        
        # Process in batches with detailed tracking
        batch_size = 50
        all_results = []
        
        for i in range(0, len(question_data), batch_size):
            batch = question_data[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(question_data) + batch_size - 1) // batch_size
            
            batch_start_time = time.time()
            current_time = datetime.now()
            
            print(f"\n🔄 Processing batch {batch_num}/{total_batches}")
            print(f"   📦 Questions {i+1}-{min(i+batch_size, total_questions)}")
            print(f"   🕐 Batch start: {current_time.strftime('%H:%M:%S')}")
            
            # Process batch in parallel
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                batch_results = list(executor.map(self.process_single_question, batch))
            
            all_results.extend(batch_results)
            
            # Batch performance metrics
            batch_time = time.time() - batch_start_time
            batch_avg_time = batch_time / len(batch)
            processed_so_far = len(all_results)
            overall_progress = processed_so_far / total_questions * 100
            
            # Time estimates
            elapsed_total = time.time() - self.start_time
            avg_time_so_far = elapsed_total / processed_so_far
            remaining_questions = total_questions - processed_so_far
            estimated_remaining_time = remaining_questions * avg_time_so_far
            estimated_completion = datetime.now() + timedelta(seconds=estimated_remaining_time)
            
            print(f"   ✅ Batch completed in {batch_time:.2f}s")
            print(f"   ⚡ Avg time per question: {batch_avg_time:.3f}s")
            print(f"   📈 Overall progress: {processed_so_far}/{total_questions} ({overall_progress:.1f}%)")
            print(f"   🕐 Estimated remaining: {estimated_remaining_time/60:.1f} minutes")
            print(f"   🎯 Estimated completion: {estimated_completion.strftime('%H:%M:%S')}")
            print(f"   📊 API success rate: {self.successful_calls}/{self.api_call_count} ({self.successful_calls/max(1,self.api_call_count)*100:.1f}%)")
        
        # Calculate final performance metrics
        total_time = time.time() - self.start_time
        end_datetime = datetime.now()
        
        performance_metrics = self._calculate_performance_metrics(all_results, total_time, total_questions)
        
        # Create submission DataFrame
        submission_data = [{'id': result.question_id, 'answer': result.answer} for result in all_results]
        submission_df = pd.DataFrame(submission_data)
        
        # Final performance report
        print("\n🎉 FINAL PROCESSING COMPLETE!")
        print("=" * 70)
        print(f"🕐 Start time: {start_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🕐 End time: {end_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️ Total processing time: {total_time/60:.2f} minutes ({total_time:.1f} seconds)")
        print(f"📊 Questions processed: {total_questions}")
        print(f"⚡ Average time per question: {performance_metrics.avg_time_per_question:.3f} seconds")
        print(f"🚀 Processing speed: {performance_metrics.questions_per_minute:.1f} questions/minute")
        print(f"🔥 Throughput: {performance_metrics.questions_per_second:.2f} questions/second")
        print(f"📡 API success rate: {performance_metrics.successful_api_calls}/{performance_metrics.successful_api_calls + performance_metrics.failed_api_calls} ({performance_metrics.successful_api_calls/(performance_metrics.successful_api_calls + performance_metrics.failed_api_calls)*100:.1f}%)")
        print(f"🎯 Average confidence: {np.mean([r.confidence for r in all_results]):.1%}")
        
        print(f"\n📈 Question Type Distribution:")
        for qtype, count in performance_metrics.question_type_distribution.items():
            percentage = count / total_questions * 100
            print(f"   {qtype}: {count} ({percentage:.1f}%)")
        
        return submission_df, performance_metrics
    
    def _calculate_performance_metrics(self, results: List[QuestionResult], total_time: float, total_questions: int) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics"""
        
        avg_time_per_question = total_time / total_questions
        questions_per_minute = total_questions / (total_time / 60)
        questions_per_second = total_questions / total_time
        
        # Question type distribution
        type_counts = {}
        for result in results:
            type_counts[result.question_type] = type_counts.get(result.question_type, 0) + 1
        
        # Confidence distribution
        confidences = [r.confidence for r in results]
        confidence_dist = {
            'high (>0.8)': len([c for c in confidences if c > 0.8]),
            'medium (0.6-0.8)': len([c for c in confidences if 0.6 <= c <= 0.8]),
            'low (<0.6)': len([c for c in confidences if c < 0.6])
        }
        
        return PerformanceMetrics(
            total_questions=total_questions,
            total_time=total_time,
            avg_time_per_question=avg_time_per_question,
            questions_per_minute=questions_per_minute,
            questions_per_second=questions_per_second,
            successful_api_calls=self.successful_calls,
            failed_api_calls=self.failed_calls,
            fallback_responses=self.failed_calls,
            confidence_distribution=confidence_dist,
            question_type_distribution=type_counts,
            processing_timeline=self.performance_log
        )
    
    def save_submission_and_metrics(self, submission_df: pd.DataFrame, metrics: PerformanceMetrics):
        """Save submission and detailed performance report"""
        
        # Save submission.csv
        submission_df.to_csv("submission.csv", index=False)
        print(f"\n✅ Submission saved: submission.csv")
        
        # Save detailed performance report
        performance_report = {
            'processing_summary': {
                'total_questions': metrics.total_questions,
                'total_time_seconds': metrics.total_time,
                'total_time_minutes': metrics.total_time / 60,
                'avg_time_per_question_seconds': metrics.avg_time_per_question,
                'questions_per_minute': metrics.questions_per_minute,
                'questions_per_second': metrics.questions_per_second,
                'timestamp': datetime.now().isoformat()
            },
            'api_performance': {
                'successful_calls': metrics.successful_api_calls,
                'failed_calls': metrics.failed_api_calls,
                'success_rate_percent': metrics.successful_api_calls / (metrics.successful_api_calls + metrics.failed_api_calls) * 100,
                'fallback_responses': metrics.fallback_responses
            },
            'question_analysis': {
                'type_distribution': metrics.question_type_distribution,
                'confidence_distribution': metrics.confidence_distribution
            }
        }
        
        with open('performance_report.json', 'w') as f:
            json.dump(performance_report, f, indent=2)
        
        print(f"📊 Performance report saved: performance_report.json")

def main():
    """Main execution function"""
    
    print("🏆 FINAL COMPETITION SUBMISSION GENERATOR")
    print("🎯 International Online Hackathon 2025")
    print("=" * 70)
    
    # Initialize processor
    api_key = "sk-ArwixD6prvk3kCwlHbRb3Mjv9wMbByoaVrOXXQDnKnQqJBCV"
    processor = FinalCompetitionProcessor(api_key, max_workers=48)
    
    # Process with full tracking
    submission_df, metrics = processor.process_with_detailed_tracking("test.csv")
    
    # Save results
    processor.save_submission_and_metrics(submission_df, metrics)
    
    print("\n🎊 COMPETITION SUBMISSION COMPLETE!")
    print("📁 Files generated:")
    print("   - submission.csv (competition submission)")
    print("   - performance_report.json (detailed metrics)")
    print("\n🏅 Ready for hackathon submission!")

if __name__ == "__main__":
    main()
