"""
Comprehensive Finetuning Pipeline for Financial Analysis Agent
Utilizes 3 datasets from data_cache folder:
1. PowerInfer/LONGCOT-Refine-500K - Chain of thought reasoning
2. Josephgflowers/Finance-Instruct-500k - Financial instruction following
3. airesearch/WangchanX-Legal-ThaiCCL-RAG - Legal compliance knowledge

Optimized for typhoon-v2-70b-instruct model with GPU acceleration
"""

import os
import json
import logging
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
from torch.cuda.amp import GradScaler, autocast
from transformers import (
    AutoTokenizer, AutoModelForCausalLM,
    TrainingArguments, Trainer,
    get_linear_schedule_with_warmup
)
from datasets import load_from_disk, load_dataset, Dataset as HFDataset, concatenate_datasets
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
from tqdm import tqdm
import gc
import random
from pathlib import Path
import warnings
warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FinancialDatasetProcessor:
    """Processes the three cached datasets for finetuning"""
    
    def __init__(self, cache_dir: str = "./data_cache"):
        self.cache_dir = Path(cache_dir)
        self.tokenizer = None
        self.max_length = 2048  # Increased from 1024 to handle longer contexts
        
    def load_tokenizer(self, model_name: str = "scb10x/typhoon-v2-70b-instruct"):
        """Load tokenizer for the model"""
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            logger.info(f"Loaded tokenizer for {model_name}")
        except Exception as e:
            logger.error(f"Failed to load tokenizer: {e}")
            # Fallback to a compatible tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained("microsoft/DialoGPT-medium")
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def load_finance_instruct_dataset(self) -> Optional[HFDataset]:
        """Load Josephgflowers/Finance-Instruct-500k dataset"""
        try:
            # First try to load from cache
            dataset_path = self.cache_dir / "Josephgflowers___finance-instruct-500k" / "default" / "0.0.0" / "379407b4708ededdf48cd33d1e1cffda45cc56f4"

            try:
                # Load using load_from_disk
                dataset = load_from_disk(str(dataset_path))
                logger.info(f"Loaded Finance-Instruct dataset from cache with {len(dataset['train'])} examples")
            except Exception as cache_error:
                # If cache fails, download fresh
                logger.info(f"Cache failed ({cache_error}), downloading Finance-Instruct dataset from Hugging Face...")
                dataset = load_dataset("Josephgflowers/Finance-Instruct-500k", cache_dir=str(self.cache_dir))
                logger.info(f"Downloaded Finance-Instruct dataset with {len(dataset['train'])} examples")

            # Convert to standard format
            def format_finance_instruct(example):
                return {
                    "input": f"System: {example['system']}\nUser: {example['user']}",
                    "output": example['assistant'],
                    "source": "finance_instruct"
                }

            formatted_dataset = dataset['train'].map(format_finance_instruct)
            return formatted_dataset

        except Exception as e:
            logger.error(f"Failed to load Finance-Instruct dataset: {e}")
            return None
    
    def load_longcot_dataset(self) -> Optional[HFDataset]:
        """Load PowerInfer/LONGCOT-Refine-500K dataset"""
        try:
            # First try to load from cache
            dataset_path = self.cache_dir / "PowerInfer___longcot-refine-500_k" / "default" / "0.0.0" / "88bf8410db01197006e572a46c88311720a23577"

            try:
                # Load using load_from_disk
                dataset = load_from_disk(str(dataset_path))
                logger.info(f"Loaded LONGCOT dataset from cache with {len(dataset['train'])} examples")
            except Exception as cache_error:
                # If cache fails, download fresh
                logger.info(f"Cache failed ({cache_error}), downloading LONGCOT dataset from Hugging Face...")
                dataset = load_dataset("PowerInfer/LONGCOT-Refine-500K", cache_dir=str(self.cache_dir))
                logger.info(f"Downloaded LONGCOT dataset with {len(dataset['train'])} examples")

            # Convert to standard format
            def format_longcot(example):
                return {
                    "input": example['prompt'],
                    "output": example['response'],
                    "source": "longcot"
                }

            formatted_dataset = dataset['train'].map(format_longcot)
            return formatted_dataset

        except Exception as e:
            logger.error(f"Failed to load LONGCOT dataset: {e}")
            return None
    
    def load_legal_rag_dataset(self) -> Optional[HFDataset]:
        """Load airesearch/WangchanX-Legal-ThaiCCL-RAG dataset"""
        try:
            # First try to load from cache
            dataset_path = self.cache_dir / "airesearch___wangchan_x-legal-thai_ccl-rag" / "default" / "0.0.0" / "654e1a10afc44ecaee36fd739bc90078dcc62388"

            try:
                # Load using load_from_disk
                dataset = load_from_disk(str(dataset_path))
                logger.info(f"Loaded Legal-RAG dataset from cache with {len(dataset['train'])} examples")
            except Exception as cache_error:
                # If cache fails, download fresh
                logger.info(f"Cache failed ({cache_error}), downloading Legal-RAG dataset from Hugging Face...")
                dataset = load_dataset("airesearch/WangchanX-Legal-ThaiCCL-RAG", cache_dir=str(self.cache_dir))
                logger.info(f"Downloaded Legal-RAG dataset with {len(dataset['train'])} examples")

            # Convert to standard format for financial-legal reasoning
            def format_legal_rag(example):
                # Create a financial-legal reasoning prompt
                contexts = []
                if example.get('positive_contexts'):
                    for ctx in example['positive_contexts'][:2]:  # Use top 2 contexts
                        contexts.append(f"Legal Context: {ctx['context']}")

                context_str = "\n".join(contexts) if contexts else ""

                input_text = f"""Legal-Financial Analysis Question: {example['question']}

{context_str}

Please provide a comprehensive analysis considering both legal and financial implications."""

                return {
                    "input": input_text,
                    "output": example.get('positive_answer', 'Analysis required based on legal and financial considerations.'),
                    "source": "legal_rag"
                }

            formatted_dataset = dataset['train'].map(format_legal_rag)
            return formatted_dataset

        except Exception as e:
            logger.error(f"Failed to load Legal-RAG dataset: {e}")
            return None

    def combine_datasets(self, max_examples_per_dataset: int = 10000) -> Optional[HFDataset]:
        """Combine all three datasets into a single training dataset"""
        datasets = []
        
        # Load each dataset
        finance_ds = self.load_finance_instruct_dataset()
        if finance_ds:
            # Sample to limit size
            if len(finance_ds) > max_examples_per_dataset:
                finance_ds = finance_ds.shuffle(seed=42).select(range(max_examples_per_dataset))
            datasets.append(finance_ds)
            logger.info(f"Added {len(finance_ds)} Finance-Instruct examples")
        
        longcot_ds = self.load_longcot_dataset()
        if longcot_ds:
            # Sample to limit size
            if len(longcot_ds) > max_examples_per_dataset:
                longcot_ds = longcot_ds.shuffle(seed=42).select(range(max_examples_per_dataset))
            datasets.append(longcot_ds)
            logger.info(f"Added {len(longcot_ds)} LONGCOT examples")
        
        legal_ds = self.load_legal_rag_dataset()
        if legal_ds:
            # Use all legal examples as they're fewer
            datasets.append(legal_ds)
            logger.info(f"Added {len(legal_ds)} Legal-RAG examples")
        
        if not datasets:
            logger.error("No datasets loaded successfully")
            return None
        
        # Combine datasets
        combined_dataset = concatenate_datasets(datasets)
        logger.info(f"Combined dataset has {len(combined_dataset)} total examples")
        
        # Shuffle the combined dataset
        combined_dataset = combined_dataset.shuffle(seed=42)
        
        return combined_dataset

    def tokenize_dataset(self, dataset: HFDataset) -> HFDataset:
        """Tokenize the combined dataset for training"""
        if not self.tokenizer:
            raise ValueError("Tokenizer not loaded. Call load_tokenizer() first.")

        def tokenize_function(example):
            # Create input-output pairs for causal language modeling
            # Format as conversation
            text = f"<|im_start|>user\n{example['input']}<|im_end|>\n<|im_start|>assistant\n{example['output']}<|im_end|>"

            # Tokenize
            tokenized = self.tokenizer(
                text,
                truncation=True,
                padding=False,
                max_length=self.max_length,
                return_tensors=None
            )

            # For causal LM, labels are the same as input_ids
            tokenized["labels"] = tokenized["input_ids"][:]

            return tokenized

        # Apply tokenization (not batched to avoid tensor shape issues)
        tokenized_dataset = dataset.map(
            tokenize_function,
            batched=False,
            remove_columns=dataset.column_names,
            desc="Tokenizing dataset"
        )

        return tokenized_dataset

class FinancialModelTrainer:
    """Handles model loading and training"""
    
    def __init__(self, model_name: str = "scb10x/typhoon-v2-70b-instruct"):
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"Using device: {self.device}")
    
    def load_model_and_tokenizer(self):
        """Load model and tokenizer"""
        try:
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Load model with appropriate settings for finetuning
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )
            
            logger.info(f"Loaded model {self.model_name}")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            # Fallback to a smaller model for testing
            logger.info("Falling back to smaller model for testing...")
            self.model_name = "microsoft/DialoGPT-medium"
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            self.model = AutoModelForCausalLM.from_pretrained(self.model_name)
    
    def setup_training_args(self, output_dir: str = "./finetuned_financial_model") -> TrainingArguments:
        """Setup training arguments"""
        return TrainingArguments(
            output_dir=output_dir,
            overwrite_output_dir=True,
            num_train_epochs=3,
            per_device_train_batch_size=2,  # Small batch size for large model
            gradient_accumulation_steps=8,  # Effective batch size = 2 * 8 = 16
            warmup_steps=500,
            logging_steps=100,
            save_steps=1000,
            eval_steps=1000,
            save_total_limit=3,
            prediction_loss_only=True,
            remove_unused_columns=False,
            dataloader_pin_memory=False,
            fp16=torch.cuda.is_available(),  # Use mixed precision if GPU available
            learning_rate=5e-5,
            weight_decay=0.01,
            adam_epsilon=1e-8,
            max_grad_norm=1.0,
            lr_scheduler_type="linear",
            report_to=None,  # Disable wandb/tensorboard for now
        )

    def train_model(self, train_dataset: HFDataset, training_args: TrainingArguments):
        """Train the model using the prepared dataset"""
        if not self.model or not self.tokenizer:
            raise ValueError("Model and tokenizer must be loaded first")

        # Custom data collator that handles padding properly
        def data_collator(features):
            # Extract input_ids and labels
            input_ids = [f["input_ids"] for f in features]
            labels = [f["labels"] for f in features]

            # Pad sequences to the same length
            max_length = max(len(ids) for ids in input_ids)

            # Pad input_ids and labels
            padded_input_ids = []
            padded_labels = []
            attention_masks = []

            for ids, lbls in zip(input_ids, labels):
                # Calculate padding needed
                padding_length = max_length - len(ids)

                # Pad input_ids
                padded_ids = ids + [self.tokenizer.pad_token_id] * padding_length
                padded_input_ids.append(padded_ids)

                # Pad labels (use -100 for padding tokens to ignore in loss)
                padded_lbls = lbls + [-100] * padding_length
                padded_labels.append(padded_lbls)

                # Create attention mask
                attention_mask = [1] * len(ids) + [0] * padding_length
                attention_masks.append(attention_mask)

            return {
                "input_ids": torch.tensor(padded_input_ids, dtype=torch.long),
                "labels": torch.tensor(padded_labels, dtype=torch.long),
                "attention_mask": torch.tensor(attention_masks, dtype=torch.long)
            }

        # Initialize trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            data_collator=data_collator,
            tokenizer=self.tokenizer,
        )

        # Start training
        logger.info("Starting training...")
        trainer.train()

        # Save the final model
        logger.info("Saving final model...")
        trainer.save_model()
        self.tokenizer.save_pretrained(training_args.output_dir)

        return trainer

def main():
    """Main finetuning pipeline"""
    logger.info("🚀 Starting Financial Model Finetuning Pipeline")
    logger.info("=" * 60)

    # Configuration
    MAX_EXAMPLES_PER_DATASET = 5000  # Adjust based on your computational resources
    OUTPUT_DIR = "./finetuned_financial_model"

    # Step 1: Initialize dataset processor
    logger.info("📊 Step 1: Initializing dataset processor...")
    processor = FinancialDatasetProcessor()

    # Step 2: Load and combine datasets
    logger.info("📚 Step 2: Loading and combining datasets...")
    combined_dataset = processor.combine_datasets(max_examples_per_dataset=MAX_EXAMPLES_PER_DATASET)

    if combined_dataset is None:
        logger.error("❌ Failed to load datasets. Exiting.")
        return

    logger.info(f"✅ Successfully combined {len(combined_dataset)} examples")

    # Step 3: Initialize model trainer
    logger.info("🤖 Step 3: Initializing model trainer...")
    trainer = FinancialModelTrainer()
    trainer.load_model_and_tokenizer()

    # Step 4: Load tokenizer for dataset processing
    logger.info("🔤 Step 4: Loading tokenizer for dataset processing...")
    processor.load_tokenizer(trainer.model_name)

    # Step 5: Tokenize dataset
    logger.info("⚙️ Step 5: Tokenizing dataset...")
    tokenized_dataset = processor.tokenize_dataset(combined_dataset)
    logger.info(f"✅ Tokenized dataset ready with {len(tokenized_dataset)} examples")

    # Step 6: Setup training arguments
    logger.info("📋 Step 6: Setting up training arguments...")
    training_args = trainer.setup_training_args(output_dir=OUTPUT_DIR)

    # Step 7: Start training
    logger.info("🏋️ Step 7: Starting model training...")
    logger.info(f"Training on {len(tokenized_dataset)} examples")
    logger.info(f"Output directory: {OUTPUT_DIR}")

    try:
        trainer.train_model(tokenized_dataset, training_args)
        logger.info("🎉 Training completed successfully!")
        logger.info(f"Model saved to: {OUTPUT_DIR}")

        # Step 8: Test the finetuned model
        logger.info("🧪 Step 8: Testing finetuned model...")
        test_finetuned_model(OUTPUT_DIR)

    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        logger.error("This might be due to insufficient GPU memory or other resource constraints.")
        logger.info("💡 Try reducing MAX_EXAMPLES_PER_DATASET or batch size in training arguments.")

def test_finetuned_model(model_path: str):
    """Test the finetuned model with a sample financial question"""
    try:
        logger.info("Loading finetuned model for testing...")

        # Load the finetuned model
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
            device_map="auto" if torch.cuda.is_available() else None,
        )

        # Test prompt
        test_prompt = """<|im_start|>user
You are a financial analysis expert. Analyze the following scenario:

A company has a current ratio of 2.5, debt-to-equity ratio of 0.4, and ROE of 15%.
The industry averages are: current ratio 2.0, debt-to-equity 0.6, and ROE 12%.

What does this tell us about the company's financial health? Choose the best answer:
A) The company is financially weak compared to industry
B) The company shows strong liquidity and profitability
C) The company has excessive debt levels
D) The company's performance is below industry standards

Final answer:<|im_end|>
<|im_start|>assistant
"""

        # Tokenize and generate
        inputs = tokenizer(test_prompt, return_tensors="pt")
        if torch.cuda.is_available():
            inputs = {k: v.cuda() for k, v in inputs.items()}

        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=200,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )

        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        logger.info("🧪 Test Response:")
        logger.info("-" * 40)
        logger.info(response[len(test_prompt):])
        logger.info("-" * 40)

    except Exception as e:
        logger.error(f"Failed to test model: {e}")

def quick_dataset_exploration():
    """Quick exploration of the datasets to understand their structure"""
    logger.info("🔍 Quick Dataset Exploration")
    logger.info("=" * 40)

    processor = FinancialDatasetProcessor()

    # Check Finance-Instruct dataset
    finance_ds = processor.load_finance_instruct_dataset()
    if finance_ds:
        logger.info("📊 Finance-Instruct Sample:")
        sample = finance_ds[0]
        logger.info(f"Input: {sample['input'][:200]}...")
        logger.info(f"Output: {sample['output'][:200]}...")
        logger.info(f"Source: {sample['source']}")
        logger.info("")

    # Check LONGCOT dataset
    longcot_ds = processor.load_longcot_dataset()
    if longcot_ds:
        logger.info("🧠 LONGCOT Sample:")
        sample = longcot_ds[0]
        logger.info(f"Input: {sample['input'][:200]}...")
        logger.info(f"Output: {sample['output'][:200]}...")
        logger.info(f"Source: {sample['source']}")
        logger.info("")

    # Check Legal-RAG dataset
    legal_ds = processor.load_legal_rag_dataset()
    if legal_ds:
        logger.info("⚖️ Legal-RAG Sample:")
        sample = legal_ds[0]
        logger.info(f"Input: {sample['input'][:200]}...")
        logger.info(f"Output: {sample['output'][:200]}...")
        logger.info(f"Source: {sample['source']}")

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Financial Model Finetuning Pipeline")
    parser.add_argument("--explore", action="store_true", help="Just explore datasets without training")
    parser.add_argument("--test-only", type=str, help="Test a specific model path")
    parser.add_argument("--max-examples", type=int, default=5000, help="Max examples per dataset")

    args = parser.parse_args()

    if args.explore:
        quick_dataset_exploration()
    elif args.test_only:
        test_finetuned_model(args.test_only)
    else:
        main()
