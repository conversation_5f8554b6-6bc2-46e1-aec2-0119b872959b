#!/usr/bin/env python3
"""
Enhanced Finetuning Runner with Configuration Support
Supports YAML configuration and command-line overrides
"""

import os
import sys
import yaml
import argparse
import logging
from pathlib import Path
from typing import Dict, Any

# Import our finetuning pipeline
from finetune_with_three_datasets import (
    FinancialDatasetProcessor, 
    FinancialModelTrainer,
    quick_dataset_exploration,
    test_finetuned_model
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_config(config_path: str = "finetuning_config.yaml") -> Dict[str, Any]:
    """Load configuration from YAML file"""
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        logger.info(f"✅ Loaded configuration from {config_path}")
        return config
    except FileNotFoundError:
        logger.warning(f"⚠️ Config file {config_path} not found. Using defaults.")
        return get_default_config()
    except Exception as e:
        logger.error(f"❌ Failed to load config: {e}")
        return get_default_config()

def get_default_config() -> Dict[str, Any]:
    """Get default configuration"""
    return {
        'model': {
            'name': 'scb10x/typhoon-v2-70b-instruct',
            'max_length': 2048
        },
        'datasets': {
            'cache_dir': './data_cache',
            'max_examples_per_dataset': 5000
        },
        'training': {
            'output_dir': './finetuned_financial_model',
            'num_train_epochs': 3,
            'per_device_train_batch_size': 2,
            'gradient_accumulation_steps': 8,
            'learning_rate': 5e-5
        },
        'hardware': {
            'use_gpu': True
        },
        'testing': {
            'test_after_training': True
        }
    }

def run_finetuning_with_config(config: Dict[str, Any]):
    """Run finetuning pipeline with configuration"""
    logger.info("🚀 Starting Configured Financial Model Finetuning")
    logger.info("=" * 60)
    
    # Extract configuration sections
    model_config = config.get('model', {})
    dataset_config = config.get('datasets', {})
    training_config = config.get('training', {})
    hardware_config = config.get('hardware', {})
    testing_config = config.get('testing', {})
    
    # Step 1: Initialize dataset processor
    logger.info("📊 Step 1: Initializing dataset processor...")
    cache_dir = dataset_config.get('cache_dir', './data_cache')
    processor = FinancialDatasetProcessor(cache_dir=cache_dir)
    
    # Step 2: Load and combine datasets
    logger.info("📚 Step 2: Loading and combining datasets...")
    max_examples = dataset_config.get('max_examples_per_dataset', 5000)
    combined_dataset = processor.combine_datasets(max_examples_per_dataset=max_examples)
    
    if combined_dataset is None:
        logger.error("❌ Failed to load datasets. Exiting.")
        return False
    
    logger.info(f"✅ Successfully combined {len(combined_dataset)} examples")
    
    # Step 3: Initialize model trainer
    logger.info("🤖 Step 3: Initializing model trainer...")
    model_name = model_config.get('name', 'scb10x/typhoon-v2-70b-instruct')
    trainer = FinancialModelTrainer(model_name=model_name)
    trainer.load_model_and_tokenizer()
    
    # Step 4: Load tokenizer for dataset processing
    logger.info("🔤 Step 4: Loading tokenizer for dataset processing...")
    processor.max_length = model_config.get('max_length', 2048)
    processor.load_tokenizer(trainer.model_name)
    
    # Step 5: Tokenize dataset
    logger.info("⚙️ Step 5: Tokenizing dataset...")
    tokenized_dataset = processor.tokenize_dataset(combined_dataset)
    logger.info(f"✅ Tokenized dataset ready with {len(tokenized_dataset)} examples")
    
    # Step 6: Setup training arguments with config
    logger.info("📋 Step 6: Setting up training arguments...")
    output_dir = training_config.get('output_dir', './finetuned_financial_model')
    
    # Create custom training args from config
    from transformers import TrainingArguments
    training_args = TrainingArguments(
        output_dir=output_dir,
        overwrite_output_dir=training_config.get('overwrite_output_dir', True),
        num_train_epochs=training_config.get('num_train_epochs', 3),
        per_device_train_batch_size=training_config.get('per_device_train_batch_size', 2),
        gradient_accumulation_steps=training_config.get('gradient_accumulation_steps', 8),
        warmup_steps=training_config.get('warmup_steps', 500),
        logging_steps=training_config.get('logging_steps', 100),
        save_steps=training_config.get('save_steps', 1000),
        save_total_limit=training_config.get('save_total_limit', 3),
        prediction_loss_only=training_config.get('prediction_loss_only', True),
        remove_unused_columns=training_config.get('remove_unused_columns', False),
        dataloader_pin_memory=training_config.get('dataloader_pin_memory', False),
        fp16=training_config.get('fp16', True) and hardware_config.get('use_gpu', True),
        learning_rate=training_config.get('learning_rate', 5e-5),
        weight_decay=training_config.get('weight_decay', 0.01),
        adam_epsilon=training_config.get('adam_epsilon', 1e-8),
        max_grad_norm=training_config.get('max_grad_norm', 1.0),
        lr_scheduler_type=training_config.get('lr_scheduler_type', 'linear'),
        report_to=None,
    )
    
    # Step 7: Start training
    logger.info("🏋️ Step 7: Starting model training...")
    logger.info(f"Training on {len(tokenized_dataset)} examples")
    logger.info(f"Output directory: {output_dir}")
    
    try:
        trainer.train_model(tokenized_dataset, training_args)
        logger.info("🎉 Training completed successfully!")
        logger.info(f"Model saved to: {output_dir}")
        
        # Step 8: Test the finetuned model if configured
        if testing_config.get('test_after_training', True):
            logger.info("🧪 Step 8: Testing finetuned model...")
            test_finetuned_model(output_dir)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        logger.error("This might be due to insufficient GPU memory or other resource constraints.")
        logger.info("💡 Try reducing max_examples_per_dataset or batch size in configuration.")
        return False

def main():
    """Main entry point with argument parsing"""
    parser = argparse.ArgumentParser(description="Enhanced Financial Model Finetuning Pipeline")
    parser.add_argument("--config", "-c", default="finetuning_config.yaml", 
                       help="Path to configuration file")
    parser.add_argument("--explore", action="store_true", 
                       help="Just explore datasets without training")
    parser.add_argument("--test-only", type=str, 
                       help="Test a specific model path")
    parser.add_argument("--max-examples", type=int, 
                       help="Override max examples per dataset")
    parser.add_argument("--output-dir", type=str,
                       help="Override output directory")
    parser.add_argument("--epochs", type=int,
                       help="Override number of training epochs")
    parser.add_argument("--batch-size", type=int,
                       help="Override batch size")
    
    args = parser.parse_args()
    
    if args.explore:
        quick_dataset_exploration()
        return
    
    if args.test_only:
        test_finetuned_model(args.test_only)
        return
    
    # Load configuration
    config = load_config(args.config)
    
    # Apply command-line overrides
    if args.max_examples:
        config['datasets']['max_examples_per_dataset'] = args.max_examples
    if args.output_dir:
        config['training']['output_dir'] = args.output_dir
    if args.epochs:
        config['training']['num_train_epochs'] = args.epochs
    if args.batch_size:
        config['training']['per_device_train_batch_size'] = args.batch_size
    
    # Run finetuning
    success = run_finetuning_with_config(config)
    
    if success:
        logger.info("🎊 Finetuning pipeline completed successfully!")
        logger.info(f"📁 Model saved to: {config['training']['output_dir']}")
        logger.info("🚀 You can now use your finetuned model for financial analysis!")
    else:
        logger.error("💥 Finetuning pipeline failed. Check logs for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
