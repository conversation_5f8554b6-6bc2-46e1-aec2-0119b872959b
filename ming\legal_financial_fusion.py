"""
Legal-Financial Knowledge Fusion Module
Combines legal compliance knowledge with financial analysis for ethical AI
"""

import json
import logging
import re
from typing import Dict, List, Any, Optional, Tuple
import random
from datasets import Dataset
import pandas as pd

logger = logging.getLogger(__name__)

class LegalFinancialFusion:
    """Fuses legal knowledge with financial analysis for comprehensive training"""
    
    def __init__(self):
        self.legal_financial_mappings = self._create_legal_financial_mappings()
        self.compliance_templates = self._create_compliance_templates()
        self.ethical_frameworks = self._create_ethical_frameworks()
    
    def _create_legal_financial_mappings(self) -> Dict[str, Dict[str, str]]:
        """Create mappings between legal concepts and financial applications"""
        return {
            "corporate_governance": {
                "legal_concept": "Board governance and director responsibilities",
                "financial_application": "Corporate governance risk assessment in investment decisions",
                "compliance_requirement": "Evaluate board composition and governance practices before investment",
                "risk_factor": "Governance risk affects long-term investment performance"
            },
            "disclosure_requirements": {
                "legal_concept": "Financial disclosure and transparency obligations",
                "financial_application": "Information transparency in financial analysis",
                "compliance_requirement": "Ensure all material information is disclosed in financial recommendations",
                "risk_factor": "Non-disclosure can lead to regulatory penalties and investor losses"
            },
            "capital_adequacy": {
                "legal_concept": "Minimum capital requirements for financial institutions",
                "financial_application": "Capital adequacy assessment in bank investments",
                "compliance_requirement": "Verify capital ratios meet regulatory minimums",
                "risk_factor": "Inadequate capital increases systemic risk"
            },
            "shareholding_limits": {
                "legal_concept": "Restrictions on shareholding percentages",
                "financial_application": "Portfolio concentration limits and diversification",
                "compliance_requirement": "Maintain shareholding below regulatory thresholds",
                "risk_factor": "Concentration risk violates regulatory limits"
            },
            "fiduciary_duty": {
                "legal_concept": "Fiduciary responsibilities to stakeholders",
                "financial_application": "Ethical investment recommendations and client interests",
                "compliance_requirement": "Prioritize client interests in all financial advice",
                "risk_factor": "Conflicts of interest compromise fiduciary duty"
            }
        }
    
    def _create_compliance_templates(self) -> List[Dict[str, str]]:
        """Create templates for legal compliance in financial analysis"""
        return [
            {
                "template_type": "investment_compliance",
                "prompt_template": """
                Investment Compliance Analysis:
                
                Legal Framework: {legal_context}
                Investment Scenario: {financial_scenario}
                
                Required Analysis:
                1. Legal compliance assessment
                2. Regulatory risk evaluation
                3. Ethical considerations
                4. Recommended actions
                
                Provide a comprehensive analysis ensuring full legal compliance.
                """,
                "response_template": """
                Compliance Analysis:
                
                Legal Assessment: {legal_assessment}
                Regulatory Compliance: {regulatory_status}
                Risk Factors: {risk_factors}
                Ethical Considerations: {ethical_analysis}
                
                Recommendation: {recommendation}
                Compliance Score: {compliance_score}/10
                """
            },
            {
                "template_type": "risk_assessment",
                "prompt_template": """
                Legal Risk Assessment for Financial Decision:
                
                Regulatory Context: {legal_context}
                Financial Decision: {financial_decision}
                
                Evaluate:
                1. Legal compliance risks
                2. Regulatory penalties potential
                3. Reputational risks
                4. Mitigation strategies
                
                Provide risk-adjusted financial analysis.
                """,
                "response_template": """
                Risk Assessment:
                
                Legal Compliance Risk: {legal_risk_level}
                Regulatory Risk: {regulatory_risk}
                Reputational Risk: {reputational_risk}
                
                Mitigation Strategies: {mitigation_strategies}
                Risk-Adjusted Recommendation: {risk_adjusted_recommendation}
                """
            }
        ]
    
    def _create_ethical_frameworks(self) -> Dict[str, List[str]]:
        """Create ethical frameworks for financial analysis"""
        return {
            "transparency_principles": [
                "Full disclosure of material information",
                "Clear explanation of risks and assumptions",
                "Transparent methodology and data sources",
                "Open communication of limitations"
            ],
            "fairness_principles": [
                "Equal treatment of all stakeholders",
                "Unbiased analysis and recommendations",
                "Fair pricing and terms",
                "Non-discriminatory practices"
            ],
            "responsibility_principles": [
                "Accountability for recommendations",
                "Consideration of societal impact",
                "Environmental and social responsibility",
                "Long-term sustainability focus"
            ]
        }
    
    def create_hybrid_training_examples(self, 
                                      legal_dataset: Dataset,
                                      financial_dataset: Dataset,
                                      num_examples: int = 1000) -> Dataset:
        """Create hybrid training examples combining legal and financial knowledge"""
        
        hybrid_examples = []
        
        # Convert datasets to lists for easier manipulation
        legal_examples = list(legal_dataset)
        financial_examples = list(financial_dataset)
        
        for i in range(num_examples):
            # Select random examples from each dataset
            legal_example = random.choice(legal_examples)
            financial_example = random.choice(financial_examples)
            
            # Create hybrid example
            hybrid_example = self._fuse_legal_financial_example(legal_example, financial_example)
            hybrid_examples.append(hybrid_example)
        
        # Create dataset from hybrid examples
        return Dataset.from_list(hybrid_examples)
    
    def _fuse_legal_financial_example(self, legal_example: Dict, financial_example: Dict) -> Dict:
        """Fuse a legal example with a financial example"""
        
        # Extract key components
        legal_question = legal_example.get('question', '')
        legal_context = self._extract_legal_context(legal_example)
        financial_instruction = financial_example.get('instruction', '')
        
        # Determine fusion type
        fusion_type = self._determine_fusion_type(legal_question, financial_instruction)
        
        # Create fused example based on type
        if fusion_type == "compliance_check":
            return self._create_compliance_check_example(legal_example, financial_example)
        elif fusion_type == "risk_assessment":
            return self._create_risk_assessment_example(legal_example, financial_example)
        elif fusion_type == "ethical_analysis":
            return self._create_ethical_analysis_example(legal_example, financial_example)
        else:
            return self._create_general_fusion_example(legal_example, financial_example)
    
    def _extract_legal_context(self, legal_example: Dict) -> str:
        """Extract relevant legal context from legal example"""
        contexts = legal_example.get('positive_contexts', [])
        if contexts:
            return contexts[0].get('context', '')[:500]  # Limit length
        return legal_example.get('positive_answer', '')[:500]
    
    def _determine_fusion_type(self, legal_question: str, financial_instruction: str) -> str:
        """Determine the type of fusion based on content"""
        
        compliance_keywords = ['compliance', 'regulation', 'legal', 'requirement', 'obligation']
        risk_keywords = ['risk', 'penalty', 'violation', 'consequence', 'liability']
        ethical_keywords = ['ethical', 'responsibility', 'transparency', 'fairness', 'duty']
        
        combined_text = (legal_question + " " + financial_instruction).lower()
        
        if any(keyword in combined_text for keyword in compliance_keywords):
            return "compliance_check"
        elif any(keyword in combined_text for keyword in risk_keywords):
            return "risk_assessment"
        elif any(keyword in combined_text for keyword in ethical_keywords):
            return "ethical_analysis"
        else:
            return "general_fusion"
    
    def _create_compliance_check_example(self, legal_example: Dict, financial_example: Dict) -> Dict:
        """Create a compliance-focused hybrid example"""
        
        legal_context = self._extract_legal_context(legal_example)
        financial_scenario = financial_example.get('instruction', '')
        
        template = random.choice([t for t in self.compliance_templates if t['template_type'] == 'investment_compliance'])
        
        instruction = template['prompt_template'].format(
            legal_context=legal_context,
            financial_scenario=financial_scenario
        )
        
        # Create compliance-focused response
        response = self._generate_compliance_response(legal_example, financial_example)
        
        return {
            'instruction': instruction,
            'input': f"Legal Context: {legal_context}\nFinancial Scenario: {financial_scenario}",
            'output': response,
            'fusion_type': 'compliance_check',
            'legal_source': legal_example.get('question', ''),
            'financial_source': financial_example.get('instruction', ''),
            'dataset_source': 'legal_financial_fusion'
        }
    
    def _create_risk_assessment_example(self, legal_example: Dict, financial_example: Dict) -> Dict:
        """Create a risk assessment hybrid example"""
        
        legal_context = self._extract_legal_context(legal_example)
        financial_decision = financial_example.get('instruction', '')
        
        template = random.choice([t for t in self.compliance_templates if t['template_type'] == 'risk_assessment'])
        
        instruction = template['prompt_template'].format(
            legal_context=legal_context,
            financial_decision=financial_decision
        )
        
        response = self._generate_risk_assessment_response(legal_example, financial_example)
        
        return {
            'instruction': instruction,
            'input': f"Regulatory Context: {legal_context}\nFinancial Decision: {financial_decision}",
            'output': response,
            'fusion_type': 'risk_assessment',
            'legal_source': legal_example.get('question', ''),
            'financial_source': financial_example.get('instruction', ''),
            'dataset_source': 'legal_financial_fusion'
        }
    
    def _create_ethical_analysis_example(self, legal_example: Dict, financial_example: Dict) -> Dict:
        """Create an ethical analysis hybrid example"""
        
        legal_context = self._extract_legal_context(legal_example)
        financial_scenario = financial_example.get('instruction', '')
        
        instruction = f"""
        Ethical Financial Analysis:
        
        Legal Framework: {legal_context}
        Financial Scenario: {financial_scenario}
        
        Conduct an ethical analysis considering:
        1. Transparency and disclosure requirements
        2. Fairness to all stakeholders
        3. Social and environmental responsibility
        4. Long-term sustainability
        
        Provide recommendations that balance financial performance with ethical considerations.
        """
        
        response = self._generate_ethical_analysis_response(legal_example, financial_example)
        
        return {
            'instruction': instruction,
            'input': f"Legal Framework: {legal_context}\nFinancial Scenario: {financial_scenario}",
            'output': response,
            'fusion_type': 'ethical_analysis',
            'legal_source': legal_example.get('question', ''),
            'financial_source': financial_example.get('instruction', ''),
            'dataset_source': 'legal_financial_fusion'
        }
    
    def _create_general_fusion_example(self, legal_example: Dict, financial_example: Dict) -> Dict:
        """Create a general fusion example"""
        
        legal_context = self._extract_legal_context(legal_example)
        financial_instruction = financial_example.get('instruction', '')
        
        instruction = f"""
        Comprehensive Financial Analysis with Legal Considerations:
        
        Financial Task: {financial_instruction}
        Legal Context: {legal_context}
        
        Provide a financial analysis that incorporates legal compliance and regulatory considerations.
        """
        
        # Combine responses from both examples
        legal_answer = legal_example.get('positive_answer', '')
        financial_answer = financial_example.get('output', '')
        
        response = f"""
        Financial Analysis with Legal Compliance:
        
        Financial Recommendation: {financial_answer}
        
        Legal Compliance Considerations: {legal_answer}
        
        Integrated Recommendation: This financial analysis incorporates both market factors and legal compliance requirements to provide a comprehensive, ethical recommendation that balances performance with regulatory adherence.
        """
        
        return {
            'instruction': instruction,
            'input': f"Financial Task: {financial_instruction}\nLegal Context: {legal_context}",
            'output': response,
            'fusion_type': 'general_fusion',
            'legal_source': legal_example.get('question', ''),
            'financial_source': financial_example.get('instruction', ''),
            'dataset_source': 'legal_financial_fusion'
        }
    
    def _generate_compliance_response(self, legal_example: Dict, financial_example: Dict) -> str:
        """Generate a compliance-focused response"""
        
        legal_answer = legal_example.get('positive_answer', '')
        financial_context = financial_example.get('instruction', '')
        
        return f"""
        Compliance Analysis:
        
        Legal Requirement: {legal_answer}
        
        Financial Application: This legal requirement must be considered when {financial_context.lower()}
        
        Compliance Assessment: 
        - Regulatory adherence is mandatory
        - Non-compliance risks include penalties and reputational damage
        - Regular monitoring and reporting required
        
        Recommendation: Ensure all financial decisions align with stated legal requirements and maintain ongoing compliance monitoring.
        
        Compliance Score: 9/10 (High compliance with proper implementation)
        """
    
    def _generate_risk_assessment_response(self, legal_example: Dict, financial_example: Dict) -> str:
        """Generate a risk assessment response"""
        
        legal_context = self._extract_legal_context(legal_example)
        
        return f"""
        Legal Risk Assessment:
        
        Regulatory Framework: {legal_context}
        
        Risk Analysis:
        - Legal Compliance Risk: Medium to High
        - Regulatory Penalty Risk: Moderate
        - Reputational Risk: High if non-compliant
        
        Mitigation Strategies:
        1. Implement robust compliance monitoring
        2. Regular legal review of financial decisions
        3. Maintain documentation of compliance efforts
        4. Establish clear escalation procedures
        
        Risk-Adjusted Recommendation: Proceed with enhanced due diligence and continuous legal oversight.
        """
    
    def _generate_ethical_analysis_response(self, legal_example: Dict, financial_example: Dict) -> str:
        """Generate an ethical analysis response"""
        
        return f"""
        Ethical Financial Analysis:
        
        Transparency Assessment: Full disclosure of legal requirements and constraints
        
        Fairness Evaluation: Ensures equal treatment of all stakeholders within legal framework
        
        Responsibility Considerations:
        - Accountability for legal compliance
        - Consideration of broader societal impact
        - Long-term sustainability focus
        
        Ethical Recommendation: Balance financial objectives with legal compliance and ethical responsibilities to create sustainable, responsible financial strategies.
        
        Ethical Score: 8.5/10 (Strong ethical foundation with legal compliance)
        """

def demo_legal_financial_fusion():
    """Demonstrate the legal-financial fusion capabilities"""
    
    print("🔗 Legal-Financial Knowledge Fusion Demo")
    print("=" * 50)
    
    # Initialize fusion module
    fusion = LegalFinancialFusion()
    
    # Create sample legal example
    sample_legal = {
        'question': 'What are the shareholding limits for financial institutions?',
        'positive_contexts': [{
            'context': 'Financial institutions must not hold more than 10% of shares in any single company without regulatory approval.'
        }],
        'positive_answer': 'Shareholding is limited to 10% without special approval to prevent concentration risk.'
    }
    
    # Create sample financial example
    sample_financial = {
        'instruction': 'Analyze the investment potential of a technology company',
        'output': 'The technology company shows strong growth potential with 15% revenue growth and solid fundamentals.'
    }
    
    # Create fusion example
    fusion_example = fusion._fuse_legal_financial_example(sample_legal, sample_financial)
    
    print("📋 Sample Fusion Example:")
    print(f"Type: {fusion_example['fusion_type']}")
    print(f"Instruction: {fusion_example['instruction'][:200]}...")
    print(f"Response: {fusion_example['output'][:300]}...")
    
    print("\n✅ Legal-Financial Fusion Demo Complete")

if __name__ == "__main__":
    demo_legal_financial_fusion()
