# Core ML and NLP libraries
torch>=2.0.0
transformers>=4.35.0
datasets>=2.14.0
tokenizers>=0.14.0
accelerate>=0.24.0

# Data processing
pandas>=1.5.0
numpy>=1.24.0
scipy>=1.10.0

# Progress and logging
tqdm>=4.65.0
wandb>=0.15.0  # Optional for experiment tracking

# GPU optimization (optional but recommended)
flash-attn>=2.3.0  # For attention optimization
bitsandbytes>=0.41.0  # For quantization

# Utility libraries
pathlib2>=2.3.7
typing-extensions>=4.5.0

# Development and testing
pytest>=7.4.0
jupyter>=1.0.0
ipywidgets>=8.0.0

# Optional: For advanced optimization
deepspeed>=0.10.0  # For distributed training
peft>=0.5.0  # For parameter-efficient fine-tuning (LoRA, etc.)
