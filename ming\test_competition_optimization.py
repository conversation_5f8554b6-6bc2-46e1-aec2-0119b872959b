"""
Test script for competition optimization
Validates the enhanced techniques and measures performance improvement
"""

import json
import logging
import pandas as pd
from datetime import datetime
import statistics

from competition_optimized_processor import CompetitionOptimizedProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_sample_test_data():
    """Create sample test data for validation"""
    
    sample_questions = [
        {
            "question": "What is the primary risk associated with high-yield bonds?",
            "choices": ["Interest rate risk", "Credit risk", "Liquidity risk"],
            "answer": "B",
            "sector": "Fixed Income"
        },
        {
            "question": "In portfolio management, what does the Sharpe ratio measure?",
            "choices": ["Risk-adjusted return", "Total return", "Volatility"],
            "answer": "A", 
            "sector": "Portfolio Management"
        },
        {
            "question": "Which derivative strategy profits from low volatility?",
            "choices": ["Long straddle", "Short straddle", "Long call"],
            "answer": "B",
            "sector": "Derivatives"
        },
        {
            "question": "What is the primary purpose of stress testing in risk management?",
            "choices": ["Measure normal market conditions", "Assess extreme scenarios", "Calculate average returns"],
            "answer": "B",
            "sector": "Risk Management"
        },
        {
            "question": "In equity valuation, what does P/E ratio represent?",
            "choices": ["Price to Earnings", "Profit to Equity", "Performance to Expectation"],
            "answer": "A",
            "sector": "Equity Investing"
        }
    ]
    
    # Create DataFrame
    df = pd.DataFrame(sample_questions)
    df.to_csv("sample_test.csv", index=False)
    
    print(f"Created sample test data with {len(sample_questions)} questions")
    return df

def test_single_question():
    """Test processing of a single question"""
    
    print("\n🧪 Testing Single Question Processing")
    print("=" * 50)
    
    # Initialize processor
    api_key = "sk-ArwixD6prvk3kCwlHbRb3Mjv9wMbByoaVrOXXQDnKnQqJBCV"
    processor = CompetitionOptimizedProcessor(api_key)
    
    # Test question
    test_question = """
    A portfolio manager is evaluating two bonds:
    Bond A: 5% coupon, 10-year maturity, trading at par
    Bond B: 3% coupon, 10-year maturity, trading at discount
    
    If interest rates are expected to rise, which bond would be preferred?
    A) Bond A, due to higher coupon payments
    B) Bond B, due to lower duration risk
    C) Both bonds are equally attractive
    """
    
    # Process with competition optimization
    result = processor.process_financial_question(
        question=test_question,
        use_self_consistency=True
    )
    
    print(f"Question: {test_question[:100]}...")
    print(f"Final Answer: {result['final_answer']}")
    print(f"Confidence: {result['confidence']:.3f}")
    print(f"All Samples: {result['all_samples']}")
    print(f"Processing Time: {result['processing_time']:.2f}s")
    print(f"Technique: {result['technique']}")
    
    return result

def test_baseline_vs_optimized():
    """Compare baseline vs optimized performance"""
    
    print("\n📊 Baseline vs Optimized Comparison")
    print("=" * 50)
    
    # Create test data
    df = create_sample_test_data()
    
    # Initialize processor
    api_key = "sk-ArwixD6prvk3kCwlHbRb3Mjv9wMbByoaVrOXXQDnKnQqJBCV"
    processor = CompetitionOptimizedProcessor(api_key)
    
    # Test baseline (single call)
    print("\n🔹 Testing Baseline (Single Call)")
    baseline_results = []
    baseline_correct = 0
    
    for idx, row in df.iterrows():
        result = processor.process_financial_question(
            question=f"{row['question']} A) {row['choices'][0]} B) {row['choices'][1]} C) {row['choices'][2]}",
            use_self_consistency=False
        )
        
        is_correct = result['final_answer'] == row['answer']
        if is_correct:
            baseline_correct += 1
            
        baseline_results.append({
            'question_id': idx,
            'predicted': result['final_answer'],
            'actual': row['answer'],
            'correct': is_correct,
            'confidence': result['confidence'],
            'time': result['processing_time']
        })
        
        print(f"Q{idx+1}: {result['final_answer']} ({'✓' if is_correct else '✗'})")
    
    baseline_accuracy = baseline_correct / len(df)
    baseline_avg_time = statistics.mean([r['time'] for r in baseline_results])
    
    # Test optimized (self-consistency)
    print("\n🔹 Testing Optimized (Self-Consistency)")
    optimized_results = []
    optimized_correct = 0
    
    for idx, row in df.iterrows():
        result = processor.process_financial_question(
            question=f"{row['question']} A) {row['choices'][0]} B) {row['choices'][1]} C) {row['choices'][2]}",
            use_self_consistency=True
        )
        
        is_correct = result['final_answer'] == row['answer']
        if is_correct:
            optimized_correct += 1
            
        optimized_results.append({
            'question_id': idx,
            'predicted': result['final_answer'],
            'actual': row['answer'],
            'correct': is_correct,
            'confidence': result['confidence'],
            'time': result['processing_time']
        })
        
        print(f"Q{idx+1}: {result['final_answer']} ({'✓' if is_correct else '✗'}) [Conf: {result['confidence']:.2f}]")
    
    optimized_accuracy = optimized_correct / len(df)
    optimized_avg_time = statistics.mean([r['time'] for r in optimized_results])
    optimized_avg_confidence = statistics.mean([r['confidence'] for r in optimized_results])
    
    # Performance comparison
    print("\n📈 PERFORMANCE COMPARISON")
    print("=" * 50)
    print(f"Baseline Accuracy:     {baseline_accuracy:.3f} ({baseline_accuracy*100:.1f}%)")
    print(f"Optimized Accuracy:    {optimized_accuracy:.3f} ({optimized_accuracy*100:.1f}%)")
    print(f"Improvement:           {((optimized_accuracy - baseline_accuracy) / baseline_accuracy * 100):+.1f}%")
    print(f"")
    print(f"Baseline Avg Time:     {baseline_avg_time:.2f}s")
    print(f"Optimized Avg Time:    {optimized_avg_time:.2f}s")
    print(f"Time Overhead:         {((optimized_avg_time - baseline_avg_time) / baseline_avg_time * 100):+.1f}%")
    print(f"")
    print(f"Optimized Avg Confidence: {optimized_avg_confidence:.3f}")
    
    # Save detailed results
    comparison_results = {
        "test_timestamp": datetime.now().isoformat(),
        "baseline": {
            "accuracy": baseline_accuracy,
            "avg_time": baseline_avg_time,
            "results": baseline_results
        },
        "optimized": {
            "accuracy": optimized_accuracy,
            "avg_time": optimized_avg_time,
            "avg_confidence": optimized_avg_confidence,
            "results": optimized_results
        },
        "improvement": {
            "accuracy_improvement": ((optimized_accuracy - baseline_accuracy) / baseline_accuracy * 100) if baseline_accuracy > 0 else 0,
            "time_overhead": ((optimized_avg_time - baseline_avg_time) / baseline_avg_time * 100) if baseline_avg_time > 0 else 0
        }
    }
    
    with open("performance_comparison.json", "w") as f:
        json.dump(comparison_results, f, indent=2)
    
    print(f"\nDetailed results saved to: performance_comparison.json")
    
    return comparison_results

def validate_competition_readiness():
    """Validate that the system is ready for competition"""
    
    print("\n🏆 Competition Readiness Validation")
    print("=" * 50)
    
    checks = []
    
    # Check 1: API connectivity
    try:
        api_key = "sk-ArwixD6prvk3kCwlHbRb3Mjv9wMbByoaVrOXXQDnKnQqJBCV"
        processor = CompetitionOptimizedProcessor(api_key)
        
        # Simple test call
        test_result = processor.call_once(
            "You are a financial expert.",
            "What is 2+2? Answer with just the number."
        )
        
        api_check = "✓" if test_result and "4" in test_result else "✗"
        checks.append(("API Connectivity", api_check))
        
    except Exception as e:
        checks.append(("API Connectivity", f"✗ ({str(e)[:50]}...)"))
    
    # Check 2: FinCoT prompt loading
    try:
        prompt = processor.get_financial_cot_prompt()
        fincot_check = "✓" if "Financial Chain-of-Thought" in prompt or "step by step" in prompt else "✗"
        checks.append(("FinCoT Prompt", fincot_check))
    except Exception as e:
        checks.append(("FinCoT Prompt", f"✗ ({str(e)[:30]}...)"))
    
    # Check 3: Self-consistency sampling
    try:
        test_samples = ["A", "A", "B", "A", "A"]
        from collections import Counter
        vote = Counter(test_samples).most_common(1)[0][0]
        consistency_check = "✓" if vote == "A" else "✗"
        checks.append(("Self-Consistency Logic", consistency_check))
    except Exception as e:
        checks.append(("Self-Consistency Logic", f"✗ ({str(e)[:30]}...)"))
    
    # Check 4: Answer extraction
    try:
        test_response = "After thinking step by step, the answer is B."
        extracted = processor.extract_choice(test_response)
        extraction_check = "✓" if extracted == "B" else "✗"
        checks.append(("Answer Extraction", extraction_check))
    except Exception as e:
        checks.append(("Answer Extraction", f"✗ ({str(e)[:30]}...)"))
    
    # Print results
    print("Validation Results:")
    for check_name, status in checks:
        print(f"  {check_name:.<30} {status}")
    
    # Overall readiness
    passed_checks = sum(1 for _, status in checks if status == "✓")
    total_checks = len(checks)
    readiness_score = passed_checks / total_checks
    
    print(f"\nOverall Readiness: {readiness_score:.1%} ({passed_checks}/{total_checks} checks passed)")
    
    if readiness_score >= 0.75:
        print("🎯 System is READY for competition!")
    else:
        print("⚠️  System needs attention before competition")
    
    return readiness_score >= 0.75

def main():
    """Main testing function"""
    
    print("🚀 Competition Optimization Testing")
    print("=" * 60)
    
    # Run tests
    try:
        # Test 1: Single question
        single_result = test_single_question()
        
        # Test 2: Baseline vs optimized
        comparison_results = test_baseline_vs_optimized()
        
        # Test 3: Competition readiness
        is_ready = validate_competition_readiness()
        
        print("\n" + "=" * 60)
        print("🎯 TESTING SUMMARY")
        print("=" * 60)
        print(f"Single Question Test: {'✓' if single_result['final_answer'] else '✗'}")
        print(f"Performance Comparison: {'✓' if comparison_results else '✗'}")
        print(f"Competition Ready: {'✓' if is_ready else '✗'}")
        
        if is_ready:
            print("\n🏆 System is optimized and ready for competition!")
            print("Key improvements implemented:")
            print("  • Financial Chain-of-Thought (FinCoT) prompting")
            print("  • Self-consistency sampling for higher accuracy")
            print("  • Sector-specific reasoning frameworks")
            print("  • Enhanced answer extraction and validation")
        else:
            print("\n⚠️  System needs further optimization")
        
    except Exception as e:
        logger.error(f"Testing failed: {str(e)}")
        print(f"\n❌ Testing failed: {str(e)}")

if __name__ == "__main__":
    main()
