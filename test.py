import os
import re
import collections
import pandas as pd
from tqdm.notebook import tqdm
from openai import OpenAI

# 1. Load test file
test_df = pd.read_csv("test.csv")

# 2. Initialize OpenAI-compatible client
client = OpenAI(
    api_key="sk-ArwixD6prvk3kCwlHbRb3Mjv9wMbByoaVrOXXQDnKnQqJBCV",  
    base_url="https://api.opentyphoon.ai/v1"
)

# 3. Constants
N_SAMPLES = 3
PROMPT_SYS = (
    "You are a helpful financial reasoning assistant. "
    "Carefully analyze the user's query and choose the correct answer (A, B, C, or D or Rise or Fall). "
    "Always end with 'Final answer: <choice>'."
)

# 4. Utility: Extract choice from output (supports A, B, C, D, Rise, Fall)
def extract_choice(response_text: str):
    cleaned_text = re.sub(r"outputs:.*?\|.*?\|", "", response_text)
    cleaned_text = cleaned_text.strip().replace("\n", " ")

    # Look for Final answer: with allowed choices
    match = re.search(r"Final answer:\s*(A|B|C|D|Rise|Fall)", cleaned_text, re.IGNORECASE)
    if match:
        val = match.group(1)
        return val.capitalize() if val.lower() in ["rise", "fall"] else val.upper()

    # Fallback: find any allowed choice anywhere in text
    find_choice = re.findall(r"\b(A|B|C|D|Rise|Fall)\b", cleaned_text, re.IGNORECASE)
    if find_choice:
        last = find_choice[-1]
        return last.capitalize() if last.lower() in ["rise", "fall"] else last.upper()

    return None

# 5. Single API call
def call_once(prompt_sys: str, prompt_usr: str) -> str:
    resp = client.chat.completions.create(
        model="typhoon-v2.1-12b-instruct",
        messages=[
            {"role": "system", "content": prompt_sys},
            {"role": "user", "content": prompt_usr},
        ],
        temperature=0.7,
        top_p=0.8,
        max_tokens=16000,
        extra_body={
            "top_k": 20,
            "min_p": 0.0,
            "chat_template_kwargs": {"enable_thinking": False},
        },
    )
    return resp.choices[0].message.content

# 6. Self-consistency sampling
def self_consistent_answer(prompt_sys, prompt_usr, k=N_SAMPLES, verbose=True):
    sub_predictions = []
    for i in range(1, k + 1):
        out = call_once(prompt_sys, prompt_usr)
        pick = extract_choice(out)
        if pick:
            sub_predictions.append(pick)
            if verbose:
                print(f"   Sample {i}: {pick}")
        else:
            if verbose:
                print(f"   Sample {i}: ❌ No valid choice found.")
    if sub_predictions:
        vote = collections.Counter(sub_predictions).most_common(1)[0][0]
        if verbose:
            print(f" ✔️ Majority vote: {vote}\n")
        return vote, sub_predictions
    # fallback
    raw = call_once(prompt_sys, prompt_usr)
    return extract_choice(raw) or None, [raw]

# 7. Run predictions
predictions = []

for i, row in tqdm(test_df.iterrows(), total=len(test_df), desc="Predicting"):
    print(f"\n🔍 Row {i+1} — ID: {row['id']}")
    final_choice, all_samples = self_consistent_answer(PROMPT_SYS, row["query"], k=N_SAMPLES, verbose=True)
    predictions.append(final_choice)

# 8. Format for submission
submission = pd.read_csv("submission.csv")
submission["answer"] = predictions
submission.to_csv("my_submission.csv", index=False)
print("\n✅ Predictions completed and saved to 'my_submission.csv'")
