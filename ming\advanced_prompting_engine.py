"""
Advanced Prompting Engine for Financial Analysis Agent
Based on hackathon staff guidance from 02_hands_on.ipynb
Implements multiple prompting techniques: SP, UST-CoT, ST-CoT, and FinCoT
"""

import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

from typhoon_client import Typhoon<PERSON><PERSON><PERSON>
from config import get_typhoon_config

logger = logging.getLogger(__name__)

class PromptingTechnique(Enum):
    """Different prompting techniques"""
    STANDARD = "standard"
    UNSTRUCTURED_COT = "unstructured_cot"
    STRUCTURED_COT = "structured_cot"
    FINANCIAL_COT = "financial_cot"

class FinancialSector(Enum):
    """Financial sectors for specialized analysis"""
    DERIVATIVES = "derivatives"
    EQUITY_INVESTING = "equity_investing"
    FIXED_INCOME = "fixed_income"
    PORTFOLIO_MANAGEMENT = "portfolio_management"
    FINANCIAL_REPORTING = "financial_reporting"
    ECONOMICS = "economics"
    QUANTITATIVE_METHODS = "quantitative_methods"
    ALTERNATIVE_INVESTMENTS = "alternative_investments"
    CORPORATE_ISSUER = "corporate_issuer"

@dataclass
class PromptResponse:
    """Response from advanced prompting"""
    technique: PromptingTechnique
    sector: FinancialSector
    raw_response: str
    parsed_answer: str
    confidence: float
    reasoning: List[str]
    thinking_process: Optional[str] = None

class AdvancedPromptingEngine:
    """Advanced prompting engine with multiple techniques"""
    
    def __init__(self, api_key: str):
        self.typhoon_client = TyphoonAPIClient(api_key)
        self.sector_workflows = self._initialize_sector_workflows()
        
    def _initialize_sector_workflows(self) -> Dict[FinancialSector, str]:
        """Initialize sector-specific workflow descriptions"""
        return {
            FinancialSector.DERIVATIVES: """
            Derivatives Analysis Workflow:
            1. Define Objective and Context (hedging, speculation, arbitrage)
            2. Identify Derivative Instrument
            3. Understand Contract Specifications (notional amount, expiration)
            4. Gather Market Data (prices, volatility, risk-free rates)
            5. Apply Valuation Models
            6. Assess Risks: Market, Counterparty, etc.
            7. Construct Payoff Diagrams or Strategies
            8. Interpret Results and Make Recommendations
            9. Review, Monitor, and Adjust Strategies
            Focus on Greeks: Delta, Gamma, Vega, Theta, Rho
            """,
            
            FinancialSector.EQUITY_INVESTING: """
            Equity Analysis Workflow:
            1. Objective Setting
            2. Market and Sector Insights (growth patterns)
            3. Industry Competitive Analysis (competitors' positions)
            4. Company Review (financial health)
            5. Valuation and Risks
            6. Investment Decision (combine insights into strategy)
            """,
            
            FinancialSector.PORTFOLIO_MANAGEMENT: """
            Portfolio Management Workflow:
            1. Define Investment Objectives
            2. Establish Investment Constraints
            3. Develop Strategic Asset Allocation
            4. Incorporate Tactical Adjustments
            5. Select and Optimize Securities
            6. Execute Implementation and Trading
            7. Measure Performance and Attribution
            8. Monitor Risk and Compliance
            9. Rebalance and Adjust Portfolio
            """,
            
            FinancialSector.FIXED_INCOME: """
            Fixed Income Analysis Workflow:
            1. Purpose and Scope (set objectives)
            2. Analyze Macro Conditions (interest rates and inflation)
            3. Assess Bond Features (duration, spread)
            4. Risk and Yield Analysis (assess scenarios)
            5. Develop Recommendations
            6. Review Performance
            """
        }
    
    def detect_financial_sector(self, query: str) -> FinancialSector:
        """Detect the financial sector from the query"""
        query_lower = query.lower()
        
        # Keywords for sector detection
        sector_keywords = {
            FinancialSector.DERIVATIVES: ['option', 'derivative', 'straddle', 'call', 'put', 'vega', 'delta', 'gamma', 'theta'],
            FinancialSector.EQUITY_INVESTING: ['stock', 'equity', 'share', 'valuation', 'pe ratio', 'dividend'],
            FinancialSector.FIXED_INCOME: ['bond', 'yield', 'duration', 'credit', 'interest rate'],
            FinancialSector.PORTFOLIO_MANAGEMENT: ['portfolio', 'allocation', 'diversification', 'rebalancing'],
            FinancialSector.ECONOMICS: ['gdp', 'inflation', 'monetary policy', 'fiscal policy', 'economic'],
            FinancialSector.FINANCIAL_REPORTING: ['financial statement', 'balance sheet', 'income statement', 'cash flow']
        }
        
        # Score each sector
        sector_scores = {}
        for sector, keywords in sector_keywords.items():
            score = sum(1 for keyword in keywords if keyword in query_lower)
            sector_scores[sector] = score
        
        # Return sector with highest score, default to derivatives if tie
        best_sector = max(sector_scores, key=sector_scores.get)
        return best_sector if sector_scores[best_sector] > 0 else FinancialSector.DERIVATIVES
    
    def generate_standard_prompt(self, query: str, sector: FinancialSector) -> Tuple[str, str]:
        """Generate standard prompt (SP)"""
        
        system_prompt = """You are a financial analyst taking a test to evaluate your knowledge of finance. You will be given a question along with possible answers. Provide clear, concise analysis and indicate the correct answer."""
        
        user_prompt = f"""
        Financial Analysis Query: {query}
        
        Please provide a comprehensive financial analysis considering:
        1. Key financial concepts and principles
        2. Relevant calculations if applicable
        3. Risk factors and considerations
        4. Clear reasoning for your conclusion
        
        Provide your analysis and recommendation.
        """
        
        return system_prompt, user_prompt
    
    def generate_unstructured_cot_prompt(self, query: str, sector: FinancialSector) -> Tuple[str, str]:
        """Generate unstructured chain-of-thought prompt (UST-CoT)"""
        
        system_prompt = """You are a financial analyst taking a test to evaluate your knowledge of finance. Before answering, you should think through the question step-by-step. Explain your reasoning at each step towards answering the question. If calculation is required, do each step of the calculation as a step in your reasoning."""
        
        user_prompt = f"""
        Financial Analysis Query: {query}
        
        Please think through this step-by-step:
        1. Identify the key financial concepts involved
        2. Analyze the given information and data
        3. Apply relevant financial theories and models
        4. Consider risk factors and market conditions
        5. Perform any necessary calculations
        6. Draw conclusions based on your analysis
        
        Walk through your reasoning process and provide your final recommendation.
        """
        
        return system_prompt, user_prompt
    
    def generate_structured_cot_prompt(self, query: str, sector: FinancialSector) -> Tuple[str, str]:
        """Generate structured chain-of-thought prompt (ST-CoT)"""
        
        system_prompt = """You are a financial analyst taking a test to evaluate your knowledge of finance. You think step by step approach with reflection to answer queries.

Follow these steps:
1. Think through the problem step by step within the <thinking> tags.
2. Provide your final, concise answer within the <output> tags.

The <thinking> sections are for your internal reasoning process only.
Do not include any part of the final answer in these sections.
The actual response to the query must be entirely contained within the <output> tags.

### Response Format:
<thinking>
[Your step-by-step reasoning process, including analysis of financial concepts, calculations, and risk assessment]
</thinking>
<output>
"analysis": [Your comprehensive financial analysis],
"recommendation": [Your final recommendation],
"confidence": [Your confidence level 0-100%]
</output>"""
        
        user_prompt = f"""
        Financial Analysis Query: {query}
        
        Analyze this financial scenario comprehensively, considering all relevant factors.
        """
        
        return system_prompt, user_prompt
    
    def generate_financial_cot_prompt(self, query: str, sector: FinancialSector) -> Tuple[str, str]:
        """Generate financial chain-of-thought prompt (FinCoT)"""
        
        workflow = self.sector_workflows.get(sector, self.sector_workflows[FinancialSector.DERIVATIVES])
        
        system_prompt = f"""You are a financial analyst taking a test to evaluate your knowledge of finance of different topics in finance. You think step by step approach with reflection to answer queries.

Follow these steps:
1. Think through the problem step by step reflect and verify while reasoning within the <thinking> tags.
2. Please and put the answer your final, concise answer within the <output> tags.

The <thinking> sections are for your internal reasoning process only.
Do not include any part of the final answer in these sections.
The actual response to the query must be entirely contained within the <output> tags.

**Sector-Specific Workflow for {sector.value.title()}:**
{workflow}

### Response Format:
<thinking>
[Follow the sector-specific workflow above. Think step by step and respond with your thinking, considering the specific sector methodology.]
</thinking>

<output>
"sector": "{sector.value}",
"analysis": [Your detailed financial analysis following the sector workflow],
"recommendation": [Your final recommendation],
"confidence": [Your confidence level 0-100%],
"key_factors": [List of key factors considered]
</output>"""
        
        user_prompt = f"""
        Financial Analysis Query: {query}
        
        Apply the {sector.value} sector methodology to analyze this scenario comprehensively.
        """
        
        return system_prompt, user_prompt
    
    def analyze_with_technique(self, query: str, technique: PromptingTechnique, 
                             sector: Optional[FinancialSector] = None) -> PromptResponse:
        """Analyze query using specified prompting technique"""
        
        if sector is None:
            sector = self.detect_financial_sector(query)
        
        # Generate appropriate prompt
        if technique == PromptingTechnique.STANDARD:
            system_prompt, user_prompt = self.generate_standard_prompt(query, sector)
        elif technique == PromptingTechnique.UNSTRUCTURED_COT:
            system_prompt, user_prompt = self.generate_unstructured_cot_prompt(query, sector)
        elif technique == PromptingTechnique.STRUCTURED_COT:
            system_prompt, user_prompt = self.generate_structured_cot_prompt(query, sector)
        elif technique == PromptingTechnique.FINANCIAL_COT:
            system_prompt, user_prompt = self.generate_financial_cot_prompt(query, sector)
        else:
            raise ValueError(f"Unknown prompting technique: {technique}")
        
        # Get response from Typhoon
        raw_response = self.typhoon_client.query(user_prompt, system_prompt, temperature=0.7)
        
        # Parse response
        parsed_answer, confidence, reasoning, thinking_process = self._parse_response(raw_response, technique)
        
        return PromptResponse(
            technique=technique,
            sector=sector,
            raw_response=raw_response,
            parsed_answer=parsed_answer,
            confidence=confidence,
            reasoning=reasoning,
            thinking_process=thinking_process
        )
    
    def _parse_response(self, response: str, technique: PromptingTechnique) -> Tuple[str, float, List[str], Optional[str]]:
        """Parse response based on technique"""
        
        thinking_process = None
        
        if technique in [PromptingTechnique.STRUCTURED_COT, PromptingTechnique.FINANCIAL_COT]:
            # Extract thinking and output sections
            if "<thinking>" in response and "</thinking>" in response:
                thinking_start = response.find("<thinking>") + len("<thinking>")
                thinking_end = response.find("</thinking>")
                thinking_process = response[thinking_start:thinking_end].strip()
            
            if "<output>" in response and "</output>" in response:
                output_start = response.find("<output>") + len("<output>")
                output_end = response.find("</output>")
                output_content = response[output_start:output_end].strip()
                
                try:
                    # Try to parse as JSON-like content
                    if '"analysis"' in output_content:
                        parsed_answer = output_content
                    else:
                        parsed_answer = output_content
                except:
                    parsed_answer = output_content
            else:
                parsed_answer = response
        else:
            parsed_answer = response
        
        # Extract confidence (simple heuristic)
        confidence = 0.75  # Default confidence
        if "confidence" in response.lower():
            import re
            confidence_match = re.search(r'confidence["\s:]*(\d+)', response.lower())
            if confidence_match:
                confidence = float(confidence_match.group(1)) / 100
        
        # Extract reasoning (simple heuristic)
        reasoning = []
        lines = response.split('\n')
        for line in lines:
            line = line.strip()
            if line and (line.startswith('1.') or line.startswith('2.') or line.startswith('3.') or 
                        line.startswith('-') or line.startswith('•')):
                reasoning.append(line)
        
        return parsed_answer, confidence, reasoning, thinking_process
    
    def compare_techniques(self, query: str, sector: Optional[FinancialSector] = None) -> Dict[PromptingTechnique, PromptResponse]:
        """Compare all prompting techniques on the same query"""
        
        if sector is None:
            sector = self.detect_financial_sector(query)
        
        results = {}
        
        for technique in PromptingTechnique:
            try:
                result = self.analyze_with_technique(query, technique, sector)
                results[technique] = result
                logger.info(f"Completed analysis with {technique.value}")
            except Exception as e:
                logger.error(f"Error with {technique.value}: {str(e)}")
        
        return results

def demo_advanced_prompting():
    """Demo the advanced prompting engine"""
    
    print("🧠 Advanced Prompting Engine Demo")
    print("=" * 60)
    print("Based on hackathon staff guidance from 02_hands_on.ipynb")
    
    # Initialize engine
    api_key = "sk-ArwixD6prvk3kCwlHbRb3Mjv9wMbByoaVrOXXQDnKnQqJBCV"
    engine = AdvancedPromptingEngine(api_key)
    
    # Test query (derivatives example from the notebook)
    test_query = """
    Analyze the following options scenario:
    
    BKLN current stock price = $510.40
    
    At-the-money (ATM) options data:
    - Call Vega: 0.320, Call Delta: 0.506, Call Premium: 9.70
    - Put Vega: 0.320, Put Delta: -0.514, Put Premium: 9.22
    
    Question: If an investor expects very little price movement in BKLN, 
    what would be the best volatility strategy and why?
    
    Consider:
    1. ATM straddle characteristics
    2. Volatility sensitivity (Vega)
    3. Appropriate strategy for low volatility expectations
    """
    
    print(f"\n📊 Test Query:")
    print(f"Sector: {engine.detect_financial_sector(test_query).value}")
    print(f"Query: {test_query[:200]}...")
    
    # Test Financial CoT (most advanced technique)
    print(f"\n🎯 Testing Financial Chain-of-Thought (FinCoT):")
    print("-" * 40)
    
    result = engine.analyze_with_technique(test_query, PromptingTechnique.FINANCIAL_COT)
    
    print(f"Sector: {result.sector.value}")
    print(f"Technique: {result.technique.value}")
    print(f"Confidence: {result.confidence:.1%}")
    
    if result.thinking_process:
        print(f"\n🤔 Thinking Process:")
        print(result.thinking_process[:500] + "..." if len(result.thinking_process) > 500 else result.thinking_process)
    
    print(f"\n💡 Analysis:")
    print(result.parsed_answer[:800] + "..." if len(result.parsed_answer) > 800 else result.parsed_answer)
    
    # Compare techniques
    print(f"\n🔄 Comparing All Techniques:")
    print("-" * 40)
    
    comparison = engine.compare_techniques(test_query)
    
    for technique, response in comparison.items():
        print(f"\n{technique.value.upper()}:")
        print(f"  Confidence: {response.confidence:.1%}")
        print(f"  Response Length: {len(response.raw_response)} chars")
        print(f"  Reasoning Points: {len(response.reasoning)}")

if __name__ == "__main__":
    demo_advanced_prompting()
