"""
Financial Analysis Agent - Hackathon Demo
International Online Hackathon 2025 - Explainable AI for Ethical Finance

This demo showcases the complete Financial Analysis Agent system with:
- Typhoon2 LLM integration
- Agentic workflows
- Explainable AI components
- Ethical financial analysis
- Dataset integration capabilities
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any

# Import our modules
from financial_agent import FinancialAnalysisAgent
from core_agent_architecture import FinancialAgentCore, AgentTask, AnalysisType
from financial_analysis_modules import InvestmentComparisonModule, RiskAssessmentModule, ESGAnalysisModule
from explainable_ai_components import ExplainabilityEngine
from dataset_integration import FinancialDatasetManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HackathonDemo:
    """Main demo class for the hackathon presentation"""
    
    def __init__(self):
        self.api_key = "sk-RD2ySZFmAa5nP2WrwQfX4Q5ArZViFEkapK4UzS2jkLq8fErt"
        self.agent = FinancialAnalysisAgent(self.api_key)
        self.agent_core = FinancialAgentCore(self.api_key)
        self.explainability_engine = ExplainabilityEngine()
        self.dataset_manager = FinancialDatasetManager()
        
        # Initialize analysis modules
        self.investment_module = InvestmentComparisonModule()
        self.risk_module = RiskAssessmentModule()
        self.esg_module = ESGAnalysisModule()
        
        print("🚀 Financial Analysis Agent - Hackathon Demo Initialized")
        print("=" * 60)
    
    def print_header(self, title: str):
        """Print a formatted header"""
        print(f"\n{'='*60}")
        print(f"🎯 {title}")
        print(f"{'='*60}")
    
    def print_section(self, title: str):
        """Print a formatted section header"""
        print(f"\n📊 {title}")
        print("-" * 40)
    
    async def run_complete_demo(self):
        """Run the complete hackathon demo"""
        
        self.print_header("FINANCIAL ANALYSIS AGENT - HACKATHON 2025")
        print("Explainable AI-powered Ethical Financial Analysis Agent")
        print("Using Typhoon2 LLM with Agentic Workflows")
        
        # Demo 1: Basic Financial Analysis
        await self.demo_basic_analysis()
        
        # Demo 2: Agentic Workflow
        await self.demo_agentic_workflow()
        
        # Demo 3: Explainable AI
        await self.demo_explainable_ai()
        
        # Demo 4: Ethical Analysis
        await self.demo_ethical_analysis()
        
        # Demo 5: Dataset Integration (if available)
        await self.demo_dataset_integration()
        
        # Final Summary
        self.demo_summary()
    
    async def demo_basic_analysis(self):
        """Demo basic financial analysis capabilities"""
        
        self.print_header("DEMO 1: BASIC FINANCIAL ANALYSIS")
        
        # Investment comparison scenario
        self.print_section("Investment Comparison Analysis")
        
        option_a = "High-growth AI startup with 50% potential return but high volatility"
        option_b = "Low-risk ESG mutual fund with 8% expected annual return"
        
        user_profile = {
            "age": 30,
            "risk_tolerance": "moderate",
            "investment_horizon": "10 years",
            "ethical_preferences": "ESG focused",
            "investment_amount": 50000
        }
        
        print(f"Scenario: Comparing investment options")
        print(f"Option A: {option_a}")
        print(f"Option B: {option_b}")
        print(f"User Profile: {user_profile}")
        
        # Perform analysis
        result = self.agent.analyze_investment_comparison(option_a, option_b, user_profile)
        
        print(f"\n✅ Analysis Complete!")
        print(f"Recommendation: {result.recommendation}")
        print(f"Confidence: {result.confidence_score:.1%}")
        print(f"Key Reasoning:")
        for i, reason in enumerate(result.reasoning[:3], 1):
            print(f"  {i}. {reason}")
    
    async def demo_agentic_workflow(self):
        """Demo agentic workflow capabilities"""
        
        self.print_header("DEMO 2: AGENTIC WORKFLOW")
        
        self.print_section("Multi-Task Agent Processing")
        
        # Create multiple tasks
        task1 = AgentTask(
            task_id="demo_task_001",
            task_type=AnalysisType.INVESTMENT_COMPARISON,
            input_data={
                "option_a": "Tesla stock (TSLA)",
                "option_b": "Vanguard ESG fund (ESGV)",
                "user_profile": {"risk_tolerance": "moderate", "ethical_preferences": "ESG focused"}
            },
            priority=1
        )
        
        task2 = AgentTask(
            task_id="demo_task_002",
            task_type=AnalysisType.ESG_ANALYSIS,
            input_data={
                "company": "Apple Inc.",
                "symbol": "AAPL"
            },
            priority=2
        )
        
        # Add tasks to agent
        await self.agent_core.add_task(task1)
        await self.agent_core.add_task(task2)
        
        print(f"Added {len([task1, task2])} tasks to agent queue")
        print(f"Agent Status: {self.agent_core.get_agent_status()}")
        
        # Process tasks
        print("\n🔄 Processing tasks with agentic workflow...")
        
        result1 = await self.agent_core.process_next_task()
        if result1:
            print(f"\n✅ Task 1 Complete: {result1.recommendation}")
            print(f"   Tools used: {result1.transparency_report.get('tools_used', [])}")
        
        result2 = await self.agent_core.process_next_task()
        if result2:
            print(f"✅ Task 2 Complete: {result2.recommendation}")
            print(f"   Confidence: {result2.confidence_score:.1%}")
        
        print(f"\nFinal Agent Status: {self.agent_core.get_agent_status()}")
    
    async def demo_explainable_ai(self):
        """Demo explainable AI capabilities"""
        
        self.print_header("DEMO 3: EXPLAINABLE AI")
        
        self.print_section("Transparency and Explainability")
        
        # Create sample decision data
        decision_data = {
            'decision_id': 'explainable_demo',
            'options': [
                {
                    'name': 'Sustainable Energy Fund',
                    'expected_return': 0.10,
                    'volatility': 0.18,
                    'esg_score': 88
                },
                {
                    'name': 'Traditional Growth Fund',
                    'expected_return': 0.12,
                    'volatility': 0.25,
                    'esg_score': 45
                }
            ],
            'user_profile': {
                'risk_tolerance': 'moderate',
                'ethical_preferences': 'ESG focused'
            }
        }
        
        analysis_result = {
            'recommendation': 'Sustainable Energy Fund',
            'confidence_score': 0.87,
            'reasoning': [
                'ESG score aligns with user preferences',
                'Risk level appropriate for moderate tolerance',
                'Sustainable focus supports long-term growth'
            ]
        }
        
        print("Generating comprehensive explanation...")
        
        # Generate explanation
        explanation = self.explainability_engine.generate_explanation(decision_data, analysis_result)
        
        print(f"\n🔍 Explanation Generated:")
        print(f"Decision ID: {explanation.decision_id}")
        print(f"Transparency Rating: {explanation.transparency_metrics['transparency_rating']}")
        print(f"Ethical Compliance: {explanation.ethical_assessment['compliance_rating']}")
        print(f"Explanation Components: {len(explanation.explanation_components)}")
        
        # Show key explanation components
        for comp in explanation.explanation_components[:3]:
            print(f"  • {comp.title} (Confidence: {comp.confidence:.1%})")
        
        print(f"\n📋 Decision Summary:")
        print(explanation.decision_summary)
    
    async def demo_ethical_analysis(self):
        """Demo ethical analysis capabilities"""
        
        self.print_header("DEMO 4: ETHICAL FINANCIAL ANALYSIS")
        
        self.print_section("ESG Analysis Module")
        
        # ESG analysis scenario
        company_data = {
            'name': 'Green Tech Corporation',
            'industry': 'renewable energy',
            'renewable_energy_usage': 95,
            'carbon_neutral_commitment': True,
            'diversity_score': 82,
            'employee_satisfaction': 88,
            'board_independence': 85,
            'transparency_rating': 92
        }
        
        print(f"Analyzing: {company_data['name']}")
        print(f"Industry: {company_data['industry']}")
        
        # Perform ESG analysis
        esg_result = self.esg_module.analyze({'company_data': company_data})
        
        print(f"\n🌱 ESG Analysis Results:")
        print(f"Overall ESG Score: {esg_result['esg_scores']['overall_score']:.1f}")
        print(f"ESG Rating: {esg_result['esg_scores']['rating']}")
        print(f"Environmental Score: {esg_result['esg_scores']['environmental_score']:.1f}")
        print(f"Social Score: {esg_result['esg_scores']['social_score']:.1f}")
        print(f"Governance Score: {esg_result['esg_scores']['governance_score']:.1f}")
        print(f"Investment Recommendation: {esg_result['investment_recommendation']}")
        
        # Risk assessment
        self.print_section("Risk Assessment Module")
        
        investment_data = {
            'name': 'Green Tech Corporation',
            'volatility': 0.22,
            'expected_return': 0.14,
            'beta': 1.3,
            'asset_type': 'stock'
        }
        
        risk_result = self.risk_module.analyze({'investment_data': investment_data})
        
        print(f"Risk Rating: {risk_result['risk_metrics'].risk_rating}")
        print(f"Volatility: {risk_result['risk_metrics'].volatility:.1%}")
        print(f"Sharpe Ratio: {risk_result['risk_metrics'].sharpe_ratio:.2f}")
        print(f"Risk Factors: {len(risk_result['risk_factors'])} identified")
    
    async def demo_dataset_integration(self):
        """Demo dataset integration capabilities"""
        
        self.print_header("DEMO 5: DATASET INTEGRATION")
        
        self.print_section("Financial Dataset Management")
        
        print("Dataset Integration Capabilities:")
        print("• PowerInfer/LONGCOT-Refine-500K - Chain of Thought reasoning")
        print("• Josephgflowers/Finance-Instruct-500k - Financial instruction data")
        print("• Preprocessing for financial analysis fine-tuning")
        print("• Ethical data handling and privacy protection")
        
        # Show dataset manager capabilities
        stats = {
            'supported_datasets': ['PowerInfer/LONGCOT-Refine-500K', 'Josephgflowers/Finance-Instruct-500k'],
            'preprocessing_features': ['Financial context extraction', 'Ethical filtering', 'Quality validation'],
            'fine_tuning_ready': True,
            'privacy_compliant': True
        }
        
        print(f"\n📊 Dataset Integration Status:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        print("\n💡 Note: Full dataset download and processing requires significant time")
        print("    Demo shows integration capabilities and preprocessing pipeline")
    
    def demo_summary(self):
        """Provide demo summary"""
        
        self.print_header("HACKATHON DEMO SUMMARY")
        
        print("🎯 Key Features Demonstrated:")
        print("✅ Typhoon2 LLM Integration with fallback mechanisms")
        print("✅ Agentic Workflows with multi-task processing")
        print("✅ Explainable AI with transparency metrics")
        print("✅ Ethical Financial Analysis (ESG, Risk Assessment)")
        print("✅ Dataset Integration for fine-tuning")
        print("✅ Comprehensive Documentation and Testing")
        
        print("\n🏆 Hackathon Objectives Met:")
        print("✅ Explainable AI-powered Financial Analysis")
        print("✅ Ethical Finance with ESG considerations")
        print("✅ Transparency and Trust in AI models")
        print("✅ Real-world financial challenge solutions")
        print("✅ Open-source tool utilization (Typhoon)")
        print("✅ Responsible AI development practices")
        
        print("\n🚀 Technical Achievements:")
        print("• Modular architecture for scalability")
        print("• Comprehensive error handling and fallbacks")
        print("• Multi-modal analysis capabilities")
        print("• Real-time explainability generation")
        print("• Ethical compliance monitoring")
        print("• Dataset integration pipeline")
        
        print("\n📈 Business Impact:")
        print("• Democratizes access to ethical financial analysis")
        print("• Reduces bias in investment recommendations")
        print("• Increases transparency in AI-driven finance")
        print("• Supports sustainable investing practices")
        print("• Enables informed financial decision-making")
        
        print(f"\n🎉 Demo completed successfully at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("Thank you for exploring our Financial Analysis Agent!")

async def main():
    """Main demo function"""
    demo = HackathonDemo()
    await demo.run_complete_demo()

if __name__ == "__main__":
    asyncio.run(main())
