"""
Typhoon API Client
Handles communication with the Typhoon2 LLM API
"""

import requests
import json
import logging
from typing import Dict, List, Any, Optional
from config import get_typhoon_config

logger = logging.getLogger(__name__)

class TyphoonAPIClient:
    """Client for interacting with Typhoon2 API"""
    
    def __init__(self, api_key: str, base_url: str = None):
        self.api_key = api_key
        self.config = get_typhoon_config()
        self.base_url = base_url or self.config["base_url"]
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def query(self, prompt: str, system_prompt: str = None, temperature: float = 0.7) -> str:
        """Query Typhoon2 model with proper API format"""

        if system_prompt is None:
            system_prompt = """You are an expert financial advisor with deep knowledge of:
            - Investment strategies and portfolio management
            - Risk assessment and mitigation
            - ESG (Environmental, Social, Governance) investing
            - Market analysis and economic principles
            - Ethical finance and responsible investing

            Always provide transparent, explainable recommendations with clear reasoning.
            Consider ethical implications and potential risks in all advice."""

        payload = {
            "model": self.config["model_name"],
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            "temperature": temperature,
            "max_tokens": min(100, self.config["max_tokens"])  # Limit to 100 tokens for efficiency
        }

        try:
            # Use the correct OpenTyphoon API endpoint
            endpoint = f"{self.base_url}/chat/completions"

            logger.info(f"Making API call to: {endpoint}")
            logger.info(f"Using model: {self.config['model_name']}")

            response = requests.post(endpoint, headers=self.headers, json=payload, timeout=30)

            logger.info(f"API Response Status: {response.status_code}")

            if response.status_code == 200:
                response_data = response.json()

                # Handle OpenAI-compatible response format
                if "choices" in response_data and len(response_data["choices"]) > 0:
                    content = response_data["choices"][0]["message"]["content"]
                    logger.info(f"API call successful, response length: {len(content)}")
                    return content
                else:
                    logger.warning(f"Unexpected response format: {response_data}")
                    return self._generate_fallback_response(prompt)

            elif response.status_code == 401:
                logger.error("API call failed: Invalid API key (401)")
                return self._generate_fallback_response(prompt)

            elif response.status_code == 429:
                logger.warning("API call failed: Rate limit exceeded (429)")
                # Raise exception so retry logic can handle it
                raise Exception("Rate limit exceeded (429)")

            elif response.status_code == 404:
                logger.error(f"API call failed: Endpoint not found (404) - {endpoint}")
                logger.error("This suggests the API endpoint or model name is incorrect")
                return self._generate_fallback_response(prompt)

            else:
                logger.error(f"API call failed with status {response.status_code}: {response.text}")
                return self._generate_fallback_response(prompt)

        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed: {str(e)}")
            return self._generate_fallback_response(prompt)

        except Exception as e:
            logger.error(f"Unexpected error in API call: {str(e)}")
            return self._generate_fallback_response(prompt)

    def query_with_system_prompt(self, user_prompt: str, system_prompt: str = None,
                               temperature: float = 0.7, max_tokens: int = 1024, max_retries: int = 3) -> str:
        """Enhanced query method for competition optimization with retry logic"""

        if system_prompt is None:
            system_prompt = """You are an expert financial advisor with deep knowledge of:
            - Investment strategies and portfolio management
            - Risk assessment and mitigation
            - ESG (Environmental, Social, Governance) investing
            - Market analysis and economic principles
            - Ethical finance and responsible investing

            Always provide transparent, explainable recommendations with clear reasoning.
            Consider ethical implications and potential risks in all advice."""

        for attempt in range(max_retries):
            try:
                payload = {
                    "model": self.config["model_name"],
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    "temperature": temperature,
                    "max_tokens": max_tokens,
                    "top_p": 0.8,
                    "top_k": 20,
                    "min_p": 0.0
                }

                endpoint = f"{self.base_url}/chat/completions"
                response = requests.post(endpoint, headers=self.headers, json=payload, timeout=30)

                if response.status_code == 200:
                    response_data = response.json()
                    if "choices" in response_data and len(response_data["choices"]) > 0:
                        return response_data["choices"][0]["message"]["content"]
                    else:
                        logger.warning(f"Unexpected response format: {response_data}")
                        return self._generate_fallback_response(user_prompt)

                elif response.status_code == 429:
                    wait_time = min(2 ** attempt, 60)
                    logger.warning(f"Rate limited. Waiting {wait_time}s before retry {attempt + 1}/{max_retries}")
                    import time
                    time.sleep(wait_time)
                    continue

                else:
                    logger.error(f"API error {response.status_code}: {response.text}")
                    if attempt == max_retries - 1:
                        return self._generate_fallback_response(user_prompt)

            except Exception as e:
                logger.error(f"Request failed (attempt {attempt + 1}): {str(e)}")
                if attempt == max_retries - 1:
                    return self._generate_fallback_response(user_prompt)
                import time
                time.sleep(1)

        return self._generate_fallback_response(user_prompt)

    def _generate_fallback_response(self, prompt: str) -> str:
        """Generate a fallback response when API is unavailable"""
        return f"""
        [FALLBACK MODE - API Unavailable]
        
        Based on your query: "{prompt[:100]}..."
        
        I would typically provide a detailed financial analysis, but the Typhoon2 API is currently unavailable.
        
        General Financial Guidance:
        1. Always diversify your portfolio across different asset classes
        2. Consider your risk tolerance and investment timeline
        3. Research ESG factors for ethical investing
        4. Consult with qualified financial advisors for personalized advice
        5. Stay informed about market conditions and economic indicators
        
        Please try again later when the API service is restored.
        """
