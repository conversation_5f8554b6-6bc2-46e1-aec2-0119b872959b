"""
Advanced Training Pipeline for Enhanced Financial Analysis Agent
Supports GPU-optimized training with multiple datasets including legal knowledge
"""

import os
import json
import logging
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
from torch.cuda.amp import GradScaler, autocast
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, 
    TrainingArguments, Trainer, DataCollatorForLanguageModeling
)
from datasets import Dataset as HFDataset
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import wandb
from tqdm import tqdm
import gc

from dataset_integration import FinancialDatasetManager
from config import get_typhoon_config, DATASET_CONFIG

logger = logging.getLogger(__name__)

class AdvancedFinancialTrainer:
    """Advanced trainer for financial analysis with legal compliance"""
    
    def __init__(self, 
                 model_name: str = "typhoon-v2-70b-instruct",
                 use_gpu: bool = True,
                 mixed_precision: bool = True,
                 gradient_accumulation_steps: int = 4):
        
        self.model_name = model_name
        self.device = torch.device("cuda" if use_gpu and torch.cuda.is_available() else "cpu")
        self.mixed_precision = mixed_precision
        self.gradient_accumulation_steps = gradient_accumulation_steps
        
        # Initialize components
        self.tokenizer = None
        self.model = None
        self.scaler = GradScaler() if mixed_precision else None
        self.dataset_manager = FinancialDatasetManager()
        
        # Training metrics
        self.training_metrics = {
            "loss_history": [],
            "accuracy_history": [],
            "legal_compliance_scores": []
        }
        
        logger.info(f"Initialized trainer on device: {self.device}")
        if torch.cuda.is_available():
            logger.info(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    def setup_model_and_tokenizer(self):
        """Setup model and tokenizer with GPU optimization"""
        try:
            logger.info(f"Loading tokenizer for {self.model_name}")
            self.tokenizer = AutoTokenizer.from_pretrained(
                "microsoft/DialoGPT-medium",  # Use a compatible tokenizer
                padding_side="left",
                truncation_side="left"
            )
            
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            logger.info(f"Loading model for {self.model_name}")
            # Use a smaller model for demonstration due to memory constraints
            self.model = AutoModelForCausalLM.from_pretrained(
                "microsoft/DialoGPT-medium",
                torch_dtype=torch.float16 if self.mixed_precision else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                low_cpu_mem_usage=True
            )
            
            # Enable gradient checkpointing for memory efficiency
            if hasattr(self.model, 'gradient_checkpointing_enable'):
                self.model.gradient_checkpointing_enable()
            
            logger.info("Model and tokenizer setup complete")
            
        except Exception as e:
            logger.error(f"Failed to setup model: {str(e)}")
            raise
    
    def prepare_datasets(self, max_examples_per_dataset: int = 1000):
        """Load and prepare all datasets for training"""
        logger.info("Loading datasets...")
        
        # Load all datasets
        datasets_loaded = {}
        datasets_loaded['longcot'] = self.dataset_manager.load_longcot_dataset()
        datasets_loaded['finance_instruct'] = self.dataset_manager.load_finance_instruct_dataset()
        datasets_loaded['legal_rag'] = self.dataset_manager.load_legal_rag_dataset()
        
        # Preprocess datasets
        logger.info("Preprocessing datasets...")
        for name, dataset in datasets_loaded.items():
            if dataset is not None:
                self.dataset_manager.preprocess_for_financial_analysis(
                    name, max_examples=max_examples_per_dataset
                )
        
        # Create combined training dataset
        train_dataset, val_dataset = self.dataset_manager.create_training_dataset()
        
        if train_dataset is None or val_dataset is None:
            raise ValueError("Failed to create training datasets")
        
        logger.info(f"Training dataset: {len(train_dataset)} examples")
        logger.info(f"Validation dataset: {len(val_dataset)} examples")
        
        return train_dataset, val_dataset
    
    def tokenize_dataset(self, dataset: HFDataset, max_length: int = 512) -> HFDataset:
        """Tokenize dataset for training"""
        def tokenize_function(examples):
            # Combine instruction, input, and output for training
            texts = []
            for i in range(len(examples['instruction'])):
                instruction = examples['instruction'][i]
                input_text = examples.get('input', [''] * len(examples['instruction']))[i]
                output = examples['output'][i]
                
                # Format as conversation
                if input_text:
                    text = f"Instruction: {instruction}\nInput: {input_text}\nResponse: {output}"
                else:
                    text = f"Instruction: {instruction}\nResponse: {output}"
                
                texts.append(text)
            
            # Tokenize
            tokenized = self.tokenizer(
                texts,
                truncation=True,
                padding=True,
                max_length=max_length,
                return_tensors="pt"
            )
            
            # Set labels for language modeling
            tokenized["labels"] = tokenized["input_ids"].clone()
            
            return tokenized
        
        return dataset.map(
            tokenize_function,
            batched=True,
            remove_columns=dataset.column_names
        )
    
    def create_training_arguments(self, output_dir: str = "./enhanced_model") -> TrainingArguments:
        """Create optimized training arguments for GPU training"""
        return TrainingArguments(
            output_dir=output_dir,
            overwrite_output_dir=True,
            
            # Training parameters
            num_train_epochs=3,
            per_device_train_batch_size=2,  # Small batch size for memory efficiency
            per_device_eval_batch_size=2,
            gradient_accumulation_steps=self.gradient_accumulation_steps,
            
            # Optimization
            learning_rate=5e-5,
            weight_decay=0.01,
            warmup_steps=100,
            
            # Memory optimization
            fp16=self.mixed_precision,
            dataloader_pin_memory=True,
            gradient_checkpointing=True,
            
            # Evaluation and saving
            evaluation_strategy="steps",
            eval_steps=100,
            save_strategy="steps",
            save_steps=200,
            save_total_limit=3,
            
            # Logging
            logging_dir=f"{output_dir}/logs",
            logging_steps=50,
            report_to=None,  # Disable wandb for now
            
            # Performance
            remove_unused_columns=False,
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
        )
    
    def train_model(self, 
                   train_dataset: HFDataset, 
                   val_dataset: HFDataset,
                   output_dir: str = "./enhanced_model") -> Dict[str, Any]:
        """Train the model with GPU optimization"""
        
        if self.model is None or self.tokenizer is None:
            raise ValueError("Model and tokenizer must be setup before training")
        
        # Tokenize datasets
        logger.info("Tokenizing datasets...")
        train_tokenized = self.tokenize_dataset(train_dataset)
        val_tokenized = self.tokenize_dataset(val_dataset)
        
        # Setup data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,  # Causal language modeling
        )
        
        # Create training arguments
        training_args = self.create_training_arguments(output_dir)
        
        # Initialize trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_tokenized,
            eval_dataset=val_tokenized,
            data_collator=data_collator,
            tokenizer=self.tokenizer,
        )
        
        # Start training
        logger.info("Starting training...")
        try:
            train_result = trainer.train()
            
            # Save the model
            trainer.save_model()
            trainer.save_state()
            
            # Log training results
            logger.info(f"Training completed. Final loss: {train_result.training_loss:.4f}")
            
            return {
                "training_loss": train_result.training_loss,
                "training_time": train_result.metrics.get("train_runtime", 0),
                "model_path": output_dir
            }
            
        except Exception as e:
            logger.error(f"Training failed: {str(e)}")
            raise
        
        finally:
            # Clean up GPU memory
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                gc.collect()
    
    def evaluate_model(self, test_dataset: HFDataset) -> Dict[str, float]:
        """Evaluate the trained model"""
        if self.model is None:
            raise ValueError("Model must be trained before evaluation")
        
        # Tokenize test dataset
        test_tokenized = self.tokenize_dataset(test_dataset)
        
        # Create data loader
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,
        )
        
        test_loader = DataLoader(
            test_tokenized,
            batch_size=2,
            collate_fn=data_collator,
            shuffle=False
        )
        
        # Evaluation
        self.model.eval()
        total_loss = 0
        total_samples = 0
        
        with torch.no_grad():
            for batch in tqdm(test_loader, desc="Evaluating"):
                batch = {k: v.to(self.device) for k, v in batch.items()}
                
                with autocast() if self.mixed_precision else torch.no_grad():
                    outputs = self.model(**batch)
                    loss = outputs.loss
                
                total_loss += loss.item() * batch['input_ids'].size(0)
                total_samples += batch['input_ids'].size(0)
        
        avg_loss = total_loss / total_samples
        perplexity = torch.exp(torch.tensor(avg_loss)).item()
        
        return {
            "eval_loss": avg_loss,
            "perplexity": perplexity,
            "num_samples": total_samples
        }

def main():
    """Main training function"""
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Initialize trainer
    trainer = AdvancedFinancialTrainer(
        use_gpu=True,
        mixed_precision=True,
        gradient_accumulation_steps=4
    )
    
    try:
        # Setup model
        trainer.setup_model_and_tokenizer()
        
        # Prepare datasets
        train_dataset, val_dataset = trainer.prepare_datasets(max_examples_per_dataset=500)
        
        # Train model
        results = trainer.train_model(train_dataset, val_dataset)
        
        # Evaluate
        eval_results = trainer.evaluate_model(val_dataset)
        
        print("Training Results:")
        print(json.dumps(results, indent=2))
        print("\nEvaluation Results:")
        print(json.dumps(eval_results, indent=2))
        
    except Exception as e:
        logger.error(f"Training pipeline failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
